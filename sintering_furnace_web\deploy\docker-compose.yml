version: '3.8'

services:
  furnace-web:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    environment:
      - PYTHONPATH=/app
    restart: unless-stopped
    devices:
      - /dev/ttyUSB0:/dev/ttyUSB0  # 串口设备映射
    privileged: true  # 需要访问串口设备
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deploy/nginx.conf:/etc/nginx/nginx.conf
      - ./deploy/ssl:/etc/nginx/ssl
    depends_on:
      - furnace-web
    restart: unless-stopped
