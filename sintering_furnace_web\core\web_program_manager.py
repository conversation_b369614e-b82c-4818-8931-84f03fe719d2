#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web版本程序管理器
适配原有程序管理器到Web架构，支持异步操作
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass

# 导入原有模块
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'sintering_furnace_control'))

from src.core.program_manager import ProgramManager as BaseProgramManager


@dataclass
class ProgramSegment:
    """程序段数据类"""
    segment: int
    target_temp: float
    ramp_rate: float
    hold_time: int


@dataclass
class ControlProgram:
    """控制程序数据类"""
    program_id: int
    name: str
    description: str
    segments: List[ProgramSegment]
    created_time: datetime
    modified_time: datetime


class WebProgramManager:
    """Web版本程序管理器"""
    
    def __init__(self, serial_manager, database_manager):
        """
        初始化Web程序管理器
        
        Args:
            serial_manager: 串口管理器
            database_manager: 数据库管理器
        """
        self.logger = logging.getLogger(__name__)
        self.serial_manager = serial_manager
        self.database_manager = database_manager
        
        # 初始化基础程序管理器
        self.base_manager = BaseProgramManager(serial_manager, database_manager)
        
        # 程序缓存
        self.program_cache = {}
        
        self.logger.info("Web程序管理器初始化完成")
    
    async def load_program_from_instrument(self, channel: int) -> Optional[ControlProgram]:
        """从仪表读取程序"""
        try:
            # 使用原有的程序管理器读取程序
            program_data = self.base_manager.read_program(channel)
            if program_data:
                # 转换为Web格式
                segments = []
                for i, segment_data in enumerate(program_data.get('segments', [])):
                    segment = ProgramSegment(
                        segment=i + 1,
                        target_temp=segment_data.get('target_temp', 0),
                        ramp_rate=segment_data.get('ramp_rate', 0),
                        hold_time=segment_data.get('hold_time', 0)
                    )
                    segments.append(segment)
                
                program = ControlProgram(
                    program_id=program_data.get('id', 0),
                    name=program_data.get('name', f'通道{channel}程序'),
                    description=program_data.get('description', ''),
                    segments=segments,
                    created_time=datetime.now(),
                    modified_time=datetime.now()
                )
                
                # 缓存程序
                self.program_cache[channel] = program
                return program
            
        except Exception as e:
            self.logger.error(f"从仪表读取程序失败: {e}")
        
        return None
    
    async def write_program_to_instrument(self, channel: int, program: ControlProgram) -> bool:
        """写入程序到仪表"""
        try:
            # 转换为原有格式
            program_data = {
                'id': program.program_id,
                'name': program.name,
                'description': program.description,
                'segments': []
            }
            
            for segment in program.segments:
                segment_data = {
                    'target_temp': segment.target_temp,
                    'ramp_rate': segment.ramp_rate,
                    'hold_time': segment.hold_time
                }
                program_data['segments'].append(segment_data)
            
            # 使用原有的程序管理器写入程序
            success = self.base_manager.write_program(channel, program_data)
            if success:
                # 更新缓存
                self.program_cache[channel] = program
                
                # 保存到数据库
                await self.save_program_to_database(program)
                
            return success
            
        except Exception as e:
            self.logger.error(f"写入程序到仪表失败: {e}")
            return False
    
    async def save_program_to_database(self, program: ControlProgram) -> bool:
        """保存程序到数据库"""
        try:
            # 转换为数据库格式
            program_data = {
                'id': program.program_id,
                'name': program.name,
                'description': program.description,
                'segments': json.dumps([
                    {
                        'segment': seg.segment,
                        'target_temp': seg.target_temp,
                        'ramp_rate': seg.ramp_rate,
                        'hold_time': seg.hold_time
                    }
                    for seg in program.segments
                ]),
                'created_time': program.created_time,
                'modified_time': datetime.now()
            }
            
            # 保存到数据库
            self.database_manager.save_program(program_data)
            return True
            
        except Exception as e:
            self.logger.error(f"保存程序到数据库失败: {e}")
            return False
    
    async def load_program_from_database(self, program_id: int) -> Optional[ControlProgram]:
        """从数据库加载程序"""
        try:
            program_data = self.database_manager.get_program(program_id)
            if program_data:
                # 解析段数据
                segments_data = json.loads(program_data.get('segments', '[]'))
                segments = []
                for seg_data in segments_data:
                    segment = ProgramSegment(
                        segment=seg_data['segment'],
                        target_temp=seg_data['target_temp'],
                        ramp_rate=seg_data['ramp_rate'],
                        hold_time=seg_data['hold_time']
                    )
                    segments.append(segment)
                
                program = ControlProgram(
                    program_id=program_data['id'],
                    name=program_data['name'],
                    description=program_data['description'],
                    segments=segments,
                    created_time=program_data['created_time'],
                    modified_time=program_data['modified_time']
                )
                
                return program
                
        except Exception as e:
            self.logger.error(f"从数据库加载程序失败: {e}")
        
        return None
    
    async def get_all_programs(self) -> List[Dict]:
        """获取所有程序列表"""
        try:
            programs = self.database_manager.get_all_programs()
            return [
                {
                    'id': prog['id'],
                    'name': prog['name'],
                    'description': prog['description'],
                    'created_time': prog['created_time'].isoformat() if isinstance(prog['created_time'], datetime) else prog['created_time'],
                    'modified_time': prog['modified_time'].isoformat() if isinstance(prog['modified_time'], datetime) else prog['modified_time']
                }
                for prog in programs
            ]
        except Exception as e:
            self.logger.error(f"获取程序列表失败: {e}")
            return []
    
    async def delete_program(self, program_id: int) -> bool:
        """删除程序"""
        try:
            self.database_manager.delete_program(program_id)
            return True
        except Exception as e:
            self.logger.error(f"删除程序失败: {e}")
            return False
    
    async def start_program(self, channel: int, program_id: Optional[int] = None) -> bool:
        """启动程序"""
        try:
            if program_id:
                # 先加载程序到仪表
                program = await self.load_program_from_database(program_id)
                if program:
                    success = await self.write_program_to_instrument(channel, program)
                    if not success:
                        return False
            
            # 启动程序
            return self.base_manager.start_program(channel)
            
        except Exception as e:
            self.logger.error(f"启动程序失败: {e}")
            return False
    
    async def stop_program(self, channel: int) -> bool:
        """停止程序"""
        try:
            return self.base_manager.stop_program(channel)
        except Exception as e:
            self.logger.error(f"停止程序失败: {e}")
            return False
    
    async def pause_program(self, channel: int) -> bool:
        """暂停程序"""
        try:
            return self.base_manager.pause_program(channel)
        except Exception as e:
            self.logger.error(f"暂停程序失败: {e}")
            return False
    
    async def get_program_status(self, channel: int) -> Dict:
        """获取程序运行状态"""
        try:
            status = self.base_manager.get_program_status(channel)
            return {
                'channel': channel,
                'running': status.get('running', False),
                'current_segment': status.get('current_segment', 0),
                'remaining_time': status.get('remaining_time', 0),
                'total_segments': status.get('total_segments', 0)
            }
        except Exception as e:
            self.logger.error(f"获取程序状态失败: {e}")
            return {
                'channel': channel,
                'running': False,
                'current_segment': 0,
                'remaining_time': 0,
                'total_segments': 0
            }
    
    def program_to_dict(self, program: ControlProgram) -> Dict:
        """将程序对象转换为字典"""
        return {
            'id': program.program_id,
            'name': program.name,
            'description': program.description,
            'segments': [
                {
                    'segment': seg.segment,
                    'target_temp': seg.target_temp,
                    'ramp_rate': seg.ramp_rate,
                    'hold_time': seg.hold_time
                }
                for seg in program.segments
            ],
            'created_time': program.created_time.isoformat(),
            'modified_time': program.modified_time.isoformat()
        }
    
    def dict_to_program(self, data: Dict) -> ControlProgram:
        """将字典转换为程序对象"""
        segments = []
        for seg_data in data.get('segments', []):
            segment = ProgramSegment(
                segment=seg_data['segment'],
                target_temp=seg_data['target_temp'],
                ramp_rate=seg_data['ramp_rate'],
                hold_time=seg_data['hold_time']
            )
            segments.append(segment)
        
        return ControlProgram(
            program_id=data['id'],
            name=data['name'],
            description=data['description'],
            segments=segments,
            created_time=datetime.fromisoformat(data['created_time']) if isinstance(data['created_time'], str) else data['created_time'],
            modified_time=datetime.fromisoformat(data['modified_time']) if isinstance(data['modified_time'], str) else data['modified_time']
        )
