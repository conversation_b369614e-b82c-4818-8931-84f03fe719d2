#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关于对话框
显示软件版本和开发信息
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTextEdit, QTabWidget, QWidget)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QPixmap


class AboutDialog(QDialog):
    """关于对话框类"""
    
    def __init__(self, parent=None):
        """
        初始化关于对话框
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self._init_ui()
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("关于")
        self.setModal(True)
        self.setFixedSize(500, 400)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 标题区域
        self._create_title_area(main_layout)
        
        # 选项卡区域
        self._create_tab_area(main_layout)
        
        # 按钮区域
        self._create_button_area(main_layout)
    
    def _create_title_area(self, parent_layout):
        """创建标题区域"""
        title_layout = QVBoxLayout()
        parent_layout.addLayout(title_layout)
        
        # 软件名称
        title_label = QLabel("8通道烧结炉控制软件")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(title_label)
        
        # 版本信息
        version_label = QLabel("版本 1.0")
        version_font = QFont()
        version_font.setPointSize(12)
        version_label.setFont(version_font)
        version_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(version_label)
        
        # 分隔线
        line = QLabel()
        line.setFixedHeight(1)
        line.setStyleSheet("background-color: #CCCCCC;")
        title_layout.addWidget(line)
    
    def _create_tab_area(self, parent_layout):
        """创建选项卡区域"""
        tab_widget = QTabWidget()
        parent_layout.addWidget(tab_widget)
        
        # 软件信息选项卡
        self._create_info_tab(tab_widget)
        
        # 系统信息选项卡
        self._create_system_tab(tab_widget)
        
        # 许可证选项卡
        self._create_license_tab(tab_widget)
    
    def _create_info_tab(self, tab_widget):
        """创建软件信息选项卡"""
        info_widget = QWidget()
        tab_widget.addTab(info_widget, "软件信息")
        
        layout = QVBoxLayout(info_widget)
        
        info_text = QTextEdit()
        info_text.setReadOnly(True)
        info_text.setHtml("""
        <h3>8通道烧结炉控制软件</h3>
        <p><b>版本:</b> 1.0</p>
        <p><b>开发日期:</b> 2025年9月</p>
        <p><b>开发者:</b> AI Assistant</p>
        <p><b>描述:</b> 用于控制8通道烧结炉系统的专业软件，支持宇电AI系列温控仪表通讯。</p>
        
        <h4>主要功能:</h4>
        <ul>
            <li>8通道实时温度监控</li>
            <li>RS485串口通讯</li>
            <li>控温程序管理</li>
            <li>材料数据库</li>
            <li>数据记录和导出</li>
            <li>实时曲线显示</li>
            <li>报警和事件记录</li>
        </ul>
        
        <h4>技术特点:</h4>
        <ul>
            <li>基于Python + PyQt5开发</li>
            <li>支持宇电AIBUS协议</li>
            <li>SQLite数据库存储</li>
            <li>模块化设计架构</li>
            <li>友好的用户界面</li>
        </ul>
        """)
        layout.addWidget(info_text)
    
    def _create_system_tab(self, tab_widget):
        """创建系统信息选项卡"""
        system_widget = QWidget()
        tab_widget.addTab(system_widget, "系统信息")
        
        layout = QVBoxLayout(system_widget)
        
        system_text = QTextEdit()
        system_text.setReadOnly(True)
        
        # 获取系统信息
        import sys
        import platform
        from PyQt5.QtCore import QT_VERSION_STR, PYQT_VERSION_STR
        
        system_info = f"""
        <h3>系统环境信息</h3>
        <p><b>操作系统:</b> {platform.system()} {platform.release()}</p>
        <p><b>处理器:</b> {platform.processor()}</p>
        <p><b>Python版本:</b> {sys.version}</p>
        <p><b>Qt版本:</b> {QT_VERSION_STR}</p>
        <p><b>PyQt版本:</b> {PYQT_VERSION_STR}</p>
        
        <h4>依赖库:</h4>
        <ul>
            <li>PyQt5 - GUI框架</li>
            <li>pyserial - 串口通讯</li>
            <li>matplotlib - 图表绘制</li>
            <li>numpy - 数值计算</li>
            <li>pandas - 数据处理</li>
            <li>sqlite3 - 数据库</li>
        </ul>
        """
        
        system_text.setHtml(system_info)
        layout.addWidget(system_text)
    
    def _create_license_tab(self, tab_widget):
        """创建许可证选项卡"""
        license_widget = QWidget()
        tab_widget.addTab(license_widget, "许可证")
        
        layout = QVBoxLayout(license_widget)
        
        license_text = QTextEdit()
        license_text.setReadOnly(True)
        license_text.setPlainText("""
MIT License

Copyright (c) 2025 Industrial Control Systems

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

本软件仅供学习和研究使用，在工业环境中使用前请进行充分测试。
开发者不对因使用本软件造成的任何损失承担责任。
        """)
        layout.addWidget(license_text)
    
    def _create_button_area(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        parent_layout.addLayout(button_layout)
        
        button_layout.addStretch()
        
        # 确定按钮
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept)
        ok_btn.setDefault(True)
        button_layout.addWidget(ok_btn)
