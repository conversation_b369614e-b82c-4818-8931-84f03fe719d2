#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据导出对话框
用于导出历史数据的界面组件
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QDateTimeEdit, QComboBox,
                            QGroupBox, QMessageBox, QProgressBar,
                            QCheckBox, QFileDialog)
from PyQt5.QtCore import Qt, QDateTime
import csv
from datetime import datetime


class ExportDialog(QDialog):
    """数据导出对话框类"""
    
    def __init__(self, database_manager, parent=None):
        """
        初始化数据导出对话框
        
        Args:
            database_manager: 数据库管理器实例
            parent: 父窗口
        """
        super().__init__(parent)
        self.database_manager = database_manager
        self._init_ui()
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("数据导出")
        self.setModal(True)
        self.resize(400, 300)
        
        main_layout = QVBoxLayout(self)
        
        # 时间范围选择
        time_group = QGroupBox("时间范围")
        main_layout.addWidget(time_group)
        
        time_layout = QVBoxLayout(time_group)
        
        # 开始时间
        start_layout = QHBoxLayout()
        time_layout.addLayout(start_layout)
        
        start_layout.addWidget(QLabel("开始时间:"))
        self.start_datetime = QDateTimeEdit()
        self.start_datetime.setDateTime(QDateTime.currentDateTime().addDays(-1))
        self.start_datetime.setCalendarPopup(True)
        start_layout.addWidget(self.start_datetime)
        
        # 结束时间
        end_layout = QHBoxLayout()
        time_layout.addLayout(end_layout)
        
        end_layout.addWidget(QLabel("结束时间:"))
        self.end_datetime = QDateTimeEdit()
        self.end_datetime.setDateTime(QDateTime.currentDateTime())
        self.end_datetime.setCalendarPopup(True)
        end_layout.addWidget(self.end_datetime)
        
        # 通道选择
        channel_group = QGroupBox("通道选择")
        main_layout.addWidget(channel_group)
        
        channel_layout = QVBoxLayout(channel_group)
        
        # 全选/全不选
        select_layout = QHBoxLayout()
        channel_layout.addLayout(select_layout)
        
        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(self._select_all_channels)
        select_layout.addWidget(select_all_btn)
        
        select_none_btn = QPushButton("全不选")
        select_none_btn.clicked.connect(self._select_no_channels)
        select_layout.addWidget(select_none_btn)
        
        select_layout.addStretch()
        
        # 通道复选框
        self.channel_checkboxes = []
        checkbox_layout = QHBoxLayout()
        channel_layout.addLayout(checkbox_layout)
        
        for i in range(8):
            checkbox = QCheckBox(f"通道{i+1}")
            checkbox.setChecked(True)
            self.channel_checkboxes.append(checkbox)
            checkbox_layout.addWidget(checkbox)
            
            if (i + 1) % 4 == 0:  # 每4个换行
                checkbox_layout = QHBoxLayout()
                channel_layout.addLayout(checkbox_layout)
        
        # 导出格式
        format_group = QGroupBox("导出格式")
        main_layout.addWidget(format_group)
        
        format_layout = QHBoxLayout(format_group)
        
        format_layout.addWidget(QLabel("文件格式:"))
        self.format_combo = QComboBox()
        self.format_combo.addItems(["CSV", "Excel"])
        format_layout.addWidget(self.format_combo)
        
        format_layout.addStretch()
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        main_layout.addLayout(button_layout)
        
        button_layout.addStretch()
        
        export_btn = QPushButton("导出")
        export_btn.clicked.connect(self._export_data)
        button_layout.addWidget(export_btn)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
    
    def _select_all_channels(self):
        """全选通道"""
        for checkbox in self.channel_checkboxes:
            checkbox.setChecked(True)
    
    def _select_no_channels(self):
        """全不选通道"""
        for checkbox in self.channel_checkboxes:
            checkbox.setChecked(False)
    
    def _export_data(self):
        """导出数据"""
        # 获取选中的通道
        selected_channels = []
        for i, checkbox in enumerate(self.channel_checkboxes):
            if checkbox.isChecked():
                selected_channels.append(i + 1)
        
        if not selected_channels:
            QMessageBox.warning(self, "警告", "请至少选择一个通道")
            return
        
        # 获取时间范围
        start_time = self.start_datetime.dateTime().toPyDateTime()
        end_time = self.end_datetime.dateTime().toPyDateTime()
        
        if start_time >= end_time:
            QMessageBox.warning(self, "警告", "开始时间必须早于结束时间")
            return
        
        # 选择保存文件
        file_format = self.format_combo.currentText()
        if file_format == "CSV":
            file_filter = "CSV files (*.csv)"
            default_name = f"furnace_data_{start_time.strftime('%Y%m%d_%H%M%S')}.csv"
        else:
            file_filter = "Excel files (*.xlsx)"
            default_name = f"furnace_data_{start_time.strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        filename, _ = QFileDialog.getSaveFileName(
            self, "保存导出文件", default_name, file_filter
        )
        
        if not filename:
            return
        
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            # 导出数据
            if file_format == "CSV":
                self._export_to_csv(filename, selected_channels, start_time, end_time)
            else:
                self._export_to_excel(filename, selected_channels, start_time, end_time)
            
            self.progress_bar.setValue(100)
            QMessageBox.information(self, "成功", f"数据导出成功: {filename}")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"数据导出失败: {e}")
        finally:
            self.progress_bar.setVisible(False)
    
    def _export_to_csv(self, filename, channels, start_time, end_time):
        """导出到CSV文件"""
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # 写入表头
            headers = ['时间戳', '通道', 'PV(°C)', 'SV(°C)', 'MV(%)', '状态', '报警']
            writer.writerow(headers)
            
            # 导出每个通道的数据
            total_channels = len(channels)
            for i, channel in enumerate(channels):
                # 获取通道数据
                data_logs = self.database_manager.get_data_logs(
                    channel=channel,
                    start_time=start_time,
                    end_time=end_time,
                    limit=10000  # 限制最大记录数
                )
                
                # 写入数据
                for log in data_logs:
                    writer.writerow([
                        log['timestamp'],
                        log['channel'],
                        log['pv'],
                        log['sv'],
                        log['mv'],
                        log['status'],
                        log.get('alarm', '')
                    ])
                
                # 更新进度
                progress = int((i + 1) / total_channels * 90)
                self.progress_bar.setValue(progress)
    
    def _export_to_excel(self, filename, channels, start_time, end_time):
        """导出到Excel文件"""
        try:
            import pandas as pd
            
            all_data = []
            
            # 收集所有数据
            total_channels = len(channels)
            for i, channel in enumerate(channels):
                data_logs = self.database_manager.get_data_logs(
                    channel=channel,
                    start_time=start_time,
                    end_time=end_time,
                    limit=10000
                )
                
                for log in data_logs:
                    all_data.append({
                        '时间戳': log['timestamp'],
                        '通道': log['channel'],
                        'PV(°C)': log['pv'],
                        'SV(°C)': log['sv'],
                        'MV(%)': log['mv'],
                        '状态': log['status'],
                        '报警': log.get('alarm', '')
                    })
                
                # 更新进度
                progress = int((i + 1) / total_channels * 90)
                self.progress_bar.setValue(progress)
            
            # 创建DataFrame并保存
            df = pd.DataFrame(all_data)
            df.to_excel(filename, index=False)
            
        except ImportError:
            # 如果没有pandas，回退到CSV格式
            QMessageBox.warning(self, "警告", "缺少pandas库，将导出为CSV格式")
            csv_filename = filename.replace('.xlsx', '.csv')
            self._export_to_csv(csv_filename, channels, start_time, end_time)
