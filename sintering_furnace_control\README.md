# 8通道烧结炉控制软件

## 项目概述

本软件用于控制8通道烧结炉系统，每个通道对应一台宇电AI系列温控仪表。软件通过RS485总线与仪表通讯，支持实时监控、程序设定、数据记录、材料库管理等功能。

## 系统架构

### 硬件组成
- 8台宇电AI系列温控仪表（如AI-718P）
- 1台工控机（Windows系统）
- RS485转USB/RS232转换器
- 8通道烧结炉体

### 软件架构
- **开发语言**: Python 3.7+
- **GUI框架**: PyQt5
- **通讯协议**: 宇电AIBUS协议（V8.0）
- **数据库**: SQLite
- **图表绘制**: matplotlib

## 功能特性

### 核心功能
- ✅ 8通道实时温度监控
- ✅ RS485串口通讯
- ✅ 控温程序管理
- ✅ 材料数据库
- ✅ 数据记录和导出
- ✅ 实时曲线显示
- ✅ 报警和事件记录

### 界面功能
- 8通道并列显示：PV（当前温度）、SV（设定值）、MV（输出值）、状态
- 实时曲线图（PV/SV随时间变化）
- 程序读写模块（支持多段程序）
- 仪表控制（START/STOP/HOLD命令）
- 配置管理（串口参数、仪表地址等）

## 安装和运行

### 环境要求
- Python 3.7 或更高版本
- Windows 10/11 操作系统

### 安装依赖
```bash
pip install PyQt5 pyserial matplotlib numpy pandas
```

### 运行应用
```bash
# 运行主应用程序
python run_app.py

# 运行基本功能测试
python test_basic.py

# 运行集成测试
python test_integration.py
```

## 使用说明

### 1. 串口配置
1. 打开软件后，点击"设置" -> "串口配置"
2. 选择正确的串口号（如COM1）
3. 设置波特率（默认9600）
4. 配置数据位、校验位、停止位
5. 点击"确定"保存配置

### 2. 连接设备
1. 确保RS485转换器已连接
2. 点击工具栏的"连接"按钮
3. 连接成功后状态栏显示"已连接"
4. 软件开始自动轮询仪表数据

### 3. 实时监控
- 主界面显示8个通道的实时数据
- 每个通道显示：当前温度(PV)、设定温度(SV)、输出功率(MV)、运行状态
- 右侧"实时曲线"选项卡显示温度变化曲线
- 可选择显示的通道和数据类型

### 4. 程序管理
1. 切换到"程序管理"选项卡
2. 点击"新建程序"创建控温程序
3. 设置程序段：目标温度、保温时间、结束标志
4. 保存程序到数据库
5. 选择程序并写入到指定通道

### 5. 控制操作
- 单通道控制：点击通道控件上的"启动"、"停止"、"暂停"按钮
- 批量控制：使用工具栏的"启动全部"、"停止全部"、"暂停全部"按钮

### 6. 数据导出
1. 点击"文件" -> "导出数据"
2. 选择时间范围和通道
3. 选择导出格式（CSV或Excel）
4. 点击"导出"保存文件

## 通讯协议

### AIBUS协议格式
- **读指令**: [Addr+80H] [82H] [参数代号] [00] [00] [校验码低] [校验码高]
- **写指令**: [Addr+80H] [43H] [参数代号] [数据低] [数据高] [校验码低] [校验码高]

### 常用参数代号
| 功能 | 参数代号 |
|------|----------|
| 读取PV/SV | 00H |
| 写入SV | 00H |
| 运行状态 | 1BH |
| 手动输出值 | 1AH |
| 程序段数据 | 50H~ |

### 校验码计算
- 读校验码 = 参数代号 × 256 + 82 + Addr
- 写校验码 = 参数代号 × 256 + 67 + 数据值 + Addr

## 目录结构

```
sintering_furnace_control/
├── main.py                 # 主程序入口
├── run_app.py             # 应用启动脚本
├── test_basic.py          # 基本功能测试
├── test_integration.py    # 集成测试
├── requirements.txt       # 依赖列表
├── README.md             # 说明文档
├── src/                  # 源代码目录
│   ├── core/            # 核心模块
│   │   ├── config_manager.py      # 配置管理
│   │   ├── monitoring_manager.py  # 监控管理
│   │   └── program_manager.py     # 程序管理
│   ├── communication/   # 通讯模块
│   │   ├── aibus_protocol.py      # AIBUS协议
│   │   └── serial_manager.py      # 串口管理
│   ├── database/        # 数据库模块
│   │   └── database_manager.py    # 数据库管理
│   ├── ui/             # 用户界面
│   │   ├── main_window.py         # 主窗口
│   │   ├── channel_widget.py      # 通道控件
│   │   ├── chart_widget.py        # 图表控件
│   │   ├── config_dialog.py       # 配置对话框
│   │   └── ...
│   └── utils/          # 工具模块
│       └── logger.py              # 日志工具
├── data/               # 数据目录
├── logs/               # 日志目录
├── exports/            # 导出目录
└── config/             # 配置目录
```

## 注意事项

1. **安全警告**: 本软件仅供学习和研究使用，在工业环境中使用前请进行充分测试
2. **硬件连接**: 确保RS485接线正确，地址设置无冲突
3. **权限要求**: 串口访问可能需要管理员权限
4. **数据备份**: 重要数据请定期备份
5. **温度范围**: 注意设备的工作温度范围，避免超温损坏

## 故障排除

### 常见问题
1. **串口连接失败**
   - 检查串口号是否正确
   - 确认设备管理器中串口状态
   - 检查RS485转换器驱动

2. **数据读取异常**
   - 检查仪表地址设置
   - 确认通讯参数匹配
   - 检查线路连接

3. **程序无法启动**
   - 检查Python版本
   - 确认依赖库已安装
   - 查看错误日志

## 技术支持

如有问题请查看日志文件（logs目录）或联系技术支持。

## 许可证

MIT License - 详见LICENSE文件
