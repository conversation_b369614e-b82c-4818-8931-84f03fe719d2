// 8通道烧结炉控制系统 - Web前端应用
class FurnaceControlApp {
    constructor() {
        this.websocket = null;
        this.isConnected = false;
        this.channelsData = {};
        this.temperatureChart = null;
        this.historyChart = null;
        this.currentTab = 'monitoring';
        this.programs = [];
        this.currentProgram = null;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.initWebSocket();
        this.initCharts();
        this.loadConfig();
        this.updateTime();
        this.createChannelCards();
        
        // 定时更新时间
        setInterval(() => this.updateTime(), 1000);
    }
    
    setupEventListeners() {
        // 标签页切换
        document.querySelectorAll('[data-tab]').forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                this.switchTab(e.target.dataset.tab);
            });
        });
        
        // 全局控制按钮
        document.getElementById('start-all-btn').addEventListener('click', () => this.controlAllChannels('START'));
        document.getElementById('pause-all-btn').addEventListener('click', () => this.controlAllChannels('HOLD'));
        document.getElementById('stop-all-btn').addEventListener('click', () => this.controlAllChannels('STOP'));
        
        // 程序管理
        document.getElementById('new-program-btn').addEventListener('click', () => this.newProgram());
        document.getElementById('add-segment-btn').addEventListener('click', () => this.addProgramSegment());
        document.getElementById('program-form').addEventListener('submit', (e) => this.saveProgram(e));
        
        // 历史数据查询
        document.getElementById('query-history-btn').addEventListener('click', () => this.queryHistoryData());
        document.getElementById('export-csv-btn').addEventListener('click', () => this.exportData('csv'));
        document.getElementById('export-excel-btn').addEventListener('click', () => this.exportData('excel'));
        
        // 配置保存
        document.getElementById('serial-config-form').addEventListener('submit', (e) => this.saveSerialConfig(e));
    }
    
    initWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;
        
        this.websocket = new WebSocket(wsUrl);
        
        this.websocket.onopen = () => {
            console.log('WebSocket连接已建立');
            this.isConnected = true;
            this.updateConnectionStatus();
        };
        
        this.websocket.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleWebSocketMessage(data);
        };
        
        this.websocket.onclose = () => {
            console.log('WebSocket连接已断开');
            this.isConnected = false;
            this.updateConnectionStatus();
            
            // 尝试重连
            setTimeout(() => this.initWebSocket(), 5000);
        };
        
        this.websocket.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.isConnected = false;
            this.updateConnectionStatus();
        };
    }
    
    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'monitoring_update':
                this.updateChannelsData(data.channels);
                break;
            case 'pong':
                // 心跳响应
                break;
            default:
                console.log('未知消息类型:', data.type);
        }
    }
    
    updateConnectionStatus() {
        const statusIcon = document.getElementById('connection-status');
        const statusText = document.getElementById('connection-text');
        
        if (this.isConnected) {
            statusIcon.className = 'bi bi-wifi';
            statusText.textContent = '已连接';
        } else {
            statusIcon.className = 'bi bi-wifi-off';
            statusText.textContent = '连接断开';
        }
    }
    
    updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        document.getElementById('current-time').textContent = timeString;
    }
    
    switchTab(tabName) {
        // 隐藏所有标签页
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        
        // 显示选中的标签页
        document.getElementById(`${tabName}-tab`).classList.add('active');
        
        // 更新导航栏状态
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        this.currentTab = tabName;
        
        // 根据标签页执行特定操作
        switch (tabName) {
            case 'programs':
                this.loadPrograms();
                break;
            case 'history':
                this.initHistoryPage();
                break;
            case 'config':
                this.loadChannelConfig();
                break;
        }
    }
    
    createChannelCards() {
        const grid = document.getElementById('channels-grid');
        grid.innerHTML = '';
        
        for (let i = 1; i <= 8; i++) {
            const card = this.createChannelCard(i);
            grid.appendChild(card);
        }
    }
    
    createChannelCard(channel) {
        const col = document.createElement('div');
        col.className = 'col-lg-3 col-md-4 col-sm-6';
        
        col.innerHTML = `
            <div class="card channel-card" id="channel-${channel}">
                <div class="card-body position-relative">
                    <div class="channel-title">
                        <i class="bi bi-thermometer-half"></i>
                        通道 ${channel}
                        <span class="status-indicator" id="status-${channel}"></span>
                    </div>
                    
                    <div class="data-row">
                        <span class="data-label">PV (当前温度):</span>
                        <span class="data-value pv-value" id="pv-${channel}">--</span>°C
                    </div>
                    
                    <div class="data-row">
                        <span class="data-label">SV (设定温度):</span>
                        <span class="data-value sv-value" id="sv-${channel}">--</span>°C
                    </div>
                    
                    <div class="data-row">
                        <span class="data-label">MV (输出功率):</span>
                        <span class="data-value mv-value" id="mv-${channel}">--</span>%
                    </div>
                    
                    <div class="data-row">
                        <span class="data-label">状态:</span>
                        <span class="data-value" id="status-text-${channel}">未知</span>
                    </div>
                    
                    <div class="control-buttons">
                        <button class="btn btn-success btn-sm" onclick="app.controlChannel(${channel}, 'START')">
                            <i class="bi bi-play-fill"></i> 启动
                        </button>
                        <button class="btn btn-warning btn-sm" onclick="app.controlChannel(${channel}, 'HOLD')">
                            <i class="bi bi-pause-fill"></i> 暂停
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="app.controlChannel(${channel}, 'STOP')">
                            <i class="bi bi-stop-fill"></i> 停止
                        </button>
                    </div>
                    
                    <div class="alarm-badge d-none" id="alarm-${channel}">
                        <i class="bi bi-exclamation-triangle-fill"></i>
                    </div>
                </div>
            </div>
        `;
        
        return col;
    }
    
    updateChannelsData(channels) {
        channels.forEach(channelData => {
            const channel = channelData.channel;
            this.channelsData[channel] = channelData;
            
            // 更新显示数据
            document.getElementById(`pv-${channel}`).textContent = channelData.pv.toFixed(1);
            document.getElementById(`sv-${channel}`).textContent = channelData.sv.toFixed(1);
            document.getElementById(`mv-${channel}`).textContent = channelData.mv.toFixed(1);
            document.getElementById(`status-text-${channel}`).textContent = this.getStatusText(channelData.status);
            
            // 更新状态指示器
            const statusIndicator = document.getElementById(`status-${channel}`);
            const channelCard = document.getElementById(`channel-${channel}`);
            
            // 移除所有状态类
            channelCard.classList.remove('running', 'stopped', 'paused', 'error');
            statusIndicator.classList.remove('status-running', 'status-stopped', 'status-paused', 'status-error');
            
            // 添加对应状态类
            switch (channelData.status) {
                case 'RUN':
                    channelCard.classList.add('running');
                    statusIndicator.classList.add('status-running');
                    break;
                case 'STOP':
                    channelCard.classList.add('stopped');
                    statusIndicator.classList.add('status-stopped');
                    break;
                case 'HOLD':
                    channelCard.classList.add('paused');
                    statusIndicator.classList.add('status-paused');
                    break;
                case 'ERROR':
                    channelCard.classList.add('error');
                    statusIndicator.classList.add('status-error');
                    break;
            }
            
            // 更新报警状态
            const alarmBadge = document.getElementById(`alarm-${channel}`);
            if (channelData.alarms && channelData.alarms.length > 0) {
                alarmBadge.classList.remove('d-none');
                alarmBadge.textContent = channelData.alarms.length;
            } else {
                alarmBadge.classList.add('d-none');
            }
        });
        
        // 更新实时图表
        this.updateTemperatureChart();
    }
    
    getStatusText(status) {
        const statusMap = {
            'RUN': '运行中',
            'STOP': '已停止',
            'HOLD': '暂停中',
            'ERROR': '故障',
            'UNKNOWN': '未知'
        };
        return statusMap[status] || '未知';
    }

    // 图表初始化
    initCharts() {
        // 实时温度图表
        const tempCtx = document.getElementById('temperature-chart').getContext('2d');
        this.temperatureChart = new Chart(tempCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            displayFormats: {
                                minute: 'HH:mm'
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '温度 (°C)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                }
            }
        });

        // 历史数据图表
        const historyCtx = document.getElementById('history-chart').getContext('2d');
        this.historyChart = new Chart(historyCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            displayFormats: {
                                hour: 'MM-DD HH:mm'
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '温度 (°C)'
                        }
                    }
                }
            }
        });

        // 初始化图表数据集
        this.initChartDatasets();
    }

    initChartDatasets() {
        const colors = [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
        ];

        // 为每个通道创建数据集
        for (let i = 1; i <= 8; i++) {
            this.temperatureChart.data.datasets.push({
                label: `通道${i}`,
                data: [],
                borderColor: colors[i - 1],
                backgroundColor: colors[i - 1] + '20',
                fill: false,
                tension: 0.1
            });
        }

        this.temperatureChart.update();
    }

    updateTemperatureChart() {
        const now = new Date();

        // 更新每个通道的数据
        for (let i = 1; i <= 8; i++) {
            const channelData = this.channelsData[i];
            if (channelData) {
                const dataset = this.temperatureChart.data.datasets[i - 1];
                dataset.data.push({
                    x: now,
                    y: channelData.pv
                });

                // 保持最近100个数据点
                if (dataset.data.length > 100) {
                    dataset.data.shift();
                }
            }
        }

        this.temperatureChart.update('none');
    }

    // 控制功能
    async controlChannel(channel, command) {
        try {
            const response = await fetch(`/api/channels/${channel}/control`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    channel: channel,
                    command: command
                })
            });

            const result = await response.json();

            if (response.ok) {
                this.showAlert('success', result.message);
            } else {
                this.showAlert('danger', result.detail || '操作失败');
            }
        } catch (error) {
            console.error('控制通道失败:', error);
            this.showAlert('danger', '网络错误，请检查连接');
        }
    }

    async controlAllChannels(command) {
        const promises = [];
        for (let i = 1; i <= 8; i++) {
            promises.push(this.controlChannel(i, command));
        }

        try {
            await Promise.all(promises);
            this.showAlert('success', `所有通道${command}命令已发送`);
        } catch (error) {
            this.showAlert('warning', '部分通道操作可能失败');
        }
    }

    // 程序管理功能
    async loadPrograms() {
        try {
            const response = await fetch('/api/programs');
            const data = await response.json();
            this.programs = data.programs || [];
            this.renderProgramsList();
        } catch (error) {
            console.error('加载程序列表失败:', error);
            this.showAlert('danger', '加载程序列表失败');
        }
    }

    renderProgramsList() {
        const list = document.getElementById('programs-list');
        list.innerHTML = '';

        this.programs.forEach(program => {
            const item = document.createElement('div');
            item.className = 'list-group-item list-group-item-action program-item';
            item.innerHTML = `
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${program.name}</h6>
                    <small>${new Date(program.modified_time).toLocaleDateString()}</small>
                </div>
                <p class="mb-1">${program.description}</p>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="app.editProgram(${program.id})">
                        <i class="bi bi-pencil"></i> 编辑
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="app.deleteProgram(${program.id})">
                        <i class="bi bi-trash"></i> 删除
                    </button>
                </div>
            `;
            list.appendChild(item);
        });
    }

    newProgram() {
        this.currentProgram = {
            id: 0,
            name: '',
            description: '',
            segments: []
        };
        this.renderProgramEditor();
    }

    async editProgram(programId) {
        try {
            const response = await fetch(`/api/programs/${programId}`);
            const program = await response.json();
            this.currentProgram = program;
            this.renderProgramEditor();
        } catch (error) {
            console.error('加载程序失败:', error);
            this.showAlert('danger', '加载程序失败');
        }
    }

    renderProgramEditor() {
        document.getElementById('program-name').value = this.currentProgram.name;
        document.getElementById('program-description').value = this.currentProgram.description;

        const segmentsContainer = document.getElementById('program-segments');
        segmentsContainer.innerHTML = '';

        this.currentProgram.segments.forEach((segment, index) => {
            this.addProgramSegmentElement(index, segment);
        });

        if (this.currentProgram.segments.length === 0) {
            this.addProgramSegment();
        }
    }

    addProgramSegment() {
        const segment = {
            segment: this.currentProgram.segments.length + 1,
            target_temp: 0,
            ramp_rate: 0,
            hold_time: 0
        };
        this.currentProgram.segments.push(segment);
        this.addProgramSegmentElement(this.currentProgram.segments.length - 1, segment);
    }

    addProgramSegmentElement(index, segment) {
        const container = document.getElementById('program-segments');
        const segmentDiv = document.createElement('div');
        segmentDiv.className = 'program-segment';
        segmentDiv.innerHTML = `
            <div class="program-segment-header">
                <span class="segment-number">段 ${segment.segment}</span>
                <i class="bi bi-x-circle remove-segment-btn" onclick="app.removeSegment(${index})"></i>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label">目标温度 (°C)</label>
                    <input type="number" class="form-control" value="${segment.target_temp}"
                           onchange="app.updateSegment(${index}, 'target_temp', this.value)">
                </div>
                <div class="col-md-4">
                    <label class="form-label">升温速率 (°C/min)</label>
                    <input type="number" class="form-control" value="${segment.ramp_rate}"
                           onchange="app.updateSegment(${index}, 'ramp_rate', this.value)">
                </div>
                <div class="col-md-4">
                    <label class="form-label">保温时间 (min)</label>
                    <input type="number" class="form-control" value="${segment.hold_time}"
                           onchange="app.updateSegment(${index}, 'hold_time', this.value)">
                </div>
            </div>
        `;
        container.appendChild(segmentDiv);
    }

    updateSegment(index, field, value) {
        this.currentProgram.segments[index][field] = parseFloat(value) || 0;
    }

    removeSegment(index) {
        this.currentProgram.segments.splice(index, 1);
        // 重新编号
        this.currentProgram.segments.forEach((seg, i) => {
            seg.segment = i + 1;
        });
        this.renderProgramEditor();
    }

    async saveProgram(event) {
        event.preventDefault();

        this.currentProgram.name = document.getElementById('program-name').value;
        this.currentProgram.description = document.getElementById('program-description').value;

        try {
            const method = this.currentProgram.id ? 'PUT' : 'POST';
            const url = this.currentProgram.id ? `/api/programs/${this.currentProgram.id}` : '/api/programs';

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(this.currentProgram)
            });

            const result = await response.json();

            if (response.ok) {
                this.showAlert('success', '程序保存成功');
                this.loadPrograms();
            } else {
                this.showAlert('danger', result.detail || '保存失败');
            }
        } catch (error) {
            console.error('保存程序失败:', error);
            this.showAlert('danger', '网络错误，保存失败');
        }
    }

    async deleteProgram(programId) {
        if (!confirm('确定要删除这个程序吗？')) {
            return;
        }

        try {
            const response = await fetch(`/api/programs/${programId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.showAlert('success', '程序删除成功');
                this.loadPrograms();
            } else {
                this.showAlert('danger', '删除失败');
            }
        } catch (error) {
            console.error('删除程序失败:', error);
            this.showAlert('danger', '网络错误，删除失败');
        }
    }

    // 历史数据功能
    initHistoryPage() {
        const now = new Date();
        const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

        document.getElementById('history-start-date').value = this.formatDateTimeLocal(yesterday);
        document.getElementById('history-end-date').value = this.formatDateTimeLocal(now);
    }

    formatDateTimeLocal(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        return `${year}-${month}-${day}T${hours}:${minutes}`;
    }

    async queryHistoryData() {
        const channel = document.getElementById('history-channel').value;
        const startDate = document.getElementById('history-start-date').value;
        const endDate = document.getElementById('history-end-date').value;

        if (!startDate || !endDate) {
            this.showAlert('warning', '请选择查询时间范围');
            return;
        }

        try {
            const params = new URLSearchParams({
                channel: channel,
                start_time: startDate,
                end_time: endDate
            });

            const response = await fetch(`/api/history?${params}`);
            const data = await response.json();

            if (response.ok) {
                this.renderHistoryChart(data.data);
            } else {
                this.showAlert('danger', data.detail || '查询失败');
            }
        } catch (error) {
            console.error('查询历史数据失败:', error);
            this.showAlert('danger', '网络错误，查询失败');
        }
    }

    renderHistoryChart(data) {
        // 清空现有数据
        this.historyChart.data.labels = [];
        this.historyChart.data.datasets = [];

        if (!data || data.length === 0) {
            this.historyChart.update();
            this.showAlert('info', '没有找到历史数据');
            return;
        }

        // 按通道分组数据
        const channelData = {};
        data.forEach(record => {
            if (!channelData[record.channel]) {
                channelData[record.channel] = [];
            }
            channelData[record.channel].push({
                x: new Date(record.timestamp),
                y: record.pv
            });
        });

        // 创建数据集
        const colors = [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
        ];

        Object.keys(channelData).forEach((channel, index) => {
            this.historyChart.data.datasets.push({
                label: `通道${channel}`,
                data: channelData[channel],
                borderColor: colors[index % colors.length],
                backgroundColor: colors[index % colors.length] + '20',
                fill: false,
                tension: 0.1
            });
        });

        this.historyChart.update();
    }

    async exportData(format) {
        const channel = document.getElementById('history-channel').value;
        const startDate = document.getElementById('history-start-date').value;
        const endDate = document.getElementById('history-end-date').value;

        if (!startDate || !endDate) {
            this.showAlert('warning', '请选择导出时间范围');
            return;
        }

        try {
            const params = new URLSearchParams({
                channel: channel,
                start_time: startDate,
                end_time: endDate,
                format: format
            });

            const response = await fetch(`/api/export?${params}`);

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `furnace_data_${new Date().toISOString().split('T')[0]}.${format}`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                this.showAlert('success', '数据导出成功');
            } else {
                this.showAlert('danger', '导出失败');
            }
        } catch (error) {
            console.error('导出数据失败:', error);
            this.showAlert('danger', '网络错误，导出失败');
        }
    }

    // 配置管理功能
    async loadConfig() {
        try {
            const response = await fetch('/api/config');
            const config = await response.json();

            if (response.ok) {
                // 更新串口配置
                document.getElementById('serial-port').value = config.serial_port || 'COM1';
                document.getElementById('baud-rate').value = config.baud_rate || '9600';

                // 更新通道配置
                this.renderChannelConfig(config.channels || []);
            }
        } catch (error) {
            console.error('加载配置失败:', error);
        }
    }

    renderChannelConfig(channels) {
        const container = document.getElementById('channel-config');
        container.innerHTML = '';

        for (let i = 1; i <= 8; i++) {
            const channelConfig = channels.find(ch => ch.channel === i) || {
                channel: i,
                address: i,
                enabled: true
            };

            const configItem = document.createElement('div');
            configItem.className = 'channel-config-item';
            configItem.innerHTML = `
                <div class="channel-config-label">通道 ${i}</div>
                <div class="channel-config-controls">
                    <label class="form-label me-2">地址:</label>
                    <input type="number" class="form-control me-2" style="width: 80px;"
                           value="${channelConfig.address}" id="address-${i}">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox"
                               ${channelConfig.enabled ? 'checked' : ''} id="enabled-${i}">
                        <label class="form-check-label">启用</label>
                    </div>
                </div>
            `;
            container.appendChild(configItem);
        }
    }

    loadChannelConfig() {
        this.loadConfig();
    }

    async saveSerialConfig(event) {
        event.preventDefault();

        const config = {
            'serial.port': document.getElementById('serial-port').value,
            'serial.baud_rate': parseInt(document.getElementById('baud-rate').value)
        };

        // 收集通道配置
        for (let i = 1; i <= 8; i++) {
            const address = document.getElementById(`address-${i}`).value;
            const enabled = document.getElementById(`enabled-${i}`).checked;

            config[`channel.${i}.address`] = parseInt(address);
            config[`channel.${i}.enabled`] = enabled;
        }

        try {
            const response = await fetch('/api/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            });

            const result = await response.json();

            if (response.ok) {
                this.showAlert('success', '配置保存成功');
            } else {
                this.showAlert('danger', result.detail || '保存失败');
            }
        } catch (error) {
            console.error('保存配置失败:', error);
            this.showAlert('danger', '网络错误，保存失败');
        }
    }

    // 工具函数
    showAlert(type, message) {
        const alertModal = new bootstrap.Modal(document.getElementById('alertModal'));
        const alertMessage = document.getElementById('alert-message');

        alertMessage.innerHTML = `
            <div class="alert alert-${type}" role="alert">
                <i class="bi bi-${this.getAlertIcon(type)}"></i>
                ${message}
            </div>
        `;

        alertModal.show();
    }

    getAlertIcon(type) {
        const icons = {
            'success': 'check-circle',
            'danger': 'exclamation-triangle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
}

// 初始化应用
let app;
document.addEventListener('DOMContentLoaded', function() {
    app = new FurnaceControlApp();
});
