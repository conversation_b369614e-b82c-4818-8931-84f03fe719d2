#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试脚本
测试整个系统的集成功能
"""

import sys
import os
import time
import random
from datetime import datetime

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

from src.core.config_manager import ConfigManager
from src.database.database_manager import DatabaseManager
from src.communication.serial_manager import SerialManager
from src.core.monitoring_manager import MonitoringManager
from src.core.program_manager import ProgramManager


class IntegrationTest:
    """集成测试类"""
    
    def __init__(self):
        """初始化测试环境"""
        self.app = QApplication(sys.argv)
        
        # 初始化组件
        self.config_manager = ConfigManager("test_integration_config.json")
        self.database_manager = DatabaseManager("test_integration.db")
        self.serial_manager = SerialManager(self.config_manager)
        self.monitoring_manager = MonitoringManager(self.config_manager, self.database_manager)
        self.program_manager = ProgramManager(self.database_manager, self.serial_manager)
        
        # 连接信号
        self._connect_signals()
        
        # 测试数据
        self.test_data_timer = QTimer()
        self.test_data_timer.timeout.connect(self._generate_test_data)
        
        print("集成测试环境初始化完成")
    
    def _connect_signals(self):
        """连接信号"""
        # 串口管理器信号
        self.serial_manager.data_received.connect(self._on_data_received)
        self.serial_manager.connection_status_changed.connect(self._on_connection_changed)
        self.serial_manager.error_occurred.connect(self._on_error)
        
        # 监控管理器信号
        self.monitoring_manager.alarm_triggered.connect(self._on_alarm)
        self.monitoring_manager.statistics_updated.connect(self._on_statistics_updated)
        
        # 程序管理器信号
        self.program_manager.program_loaded.connect(self._on_program_loaded)
        self.program_manager.program_started.connect(self._on_program_started)
    
    def _on_data_received(self, channel, data):
        """处理接收到的数据"""
        print(f"通道{channel}数据: PV={data.get('pv', 0):.1f}°C, SV={data.get('sv', 0):.1f}°C, MV={data.get('mv', 0)}%, 状态={data.get('status', 'UNKNOWN')}")
        
        # 传递给监控管理器
        self.monitoring_manager.process_channel_data(channel, data)
    
    def _on_connection_changed(self, connected):
        """连接状态变化"""
        status = "已连接" if connected else "已断开"
        print(f"串口连接状态: {status}")
    
    def _on_error(self, error_message):
        """错误处理"""
        print(f"错误: {error_message}")
    
    def _on_alarm(self, channel, alarm):
        """报警处理"""
        print(f"报警 - 通道{channel}: {alarm['message']} (级别: {alarm['level']})")
    
    def _on_statistics_updated(self, stats):
        """统计信息更新"""
        print(f"统计: 样本数={stats['total_samples']}, 活跃通道={stats['active_channels']}, 报警数={stats['alarm_count']}")
    
    def _on_program_loaded(self, channel, program):
        """程序加载完成"""
        print(f"程序加载完成 - 通道{channel}: {program['name']}")
    
    def _on_program_started(self, channel):
        """程序启动"""
        print(f"程序启动 - 通道{channel}")
    
    def _generate_test_data(self):
        """生成测试数据"""
        for channel in range(1, 5):  # 测试前4个通道
            # 生成模拟数据
            base_temp = 20 + channel * 10
            pv = base_temp + random.uniform(-5, 15)
            sv = base_temp + 10
            mv = random.randint(0, 100)
            status = random.choice(['RUN', 'STOP', 'HOLD'])
            
            data = {
                'pv': pv,
                'sv': sv,
                'mv': mv,
                'status': status,
                'timestamp': time.time()
            }
            
            # 模拟数据接收
            self.serial_manager.data_received.emit(channel, data)
    
    def test_database_operations(self):
        """测试数据库操作"""
        print("\n=== 测试数据库操作 ===")
        
        # 添加测试材料
        try:
            material_id = self.database_manager.add_material(
                name="集成测试材料",
                melting_point=1200.0,
                sintering_temp=1000.0,
                description="用于集成测试的材料"
            )
            print(f"添加材料成功: ID {material_id}")
        except Exception as e:
            print(f"添加材料失败: {e}")
        
        # 添加测试程序
        try:
            segments = [
                {"temp": 100, "time": 10, "end_flag": 0},
                {"temp": 200, "time": 20, "end_flag": 0},
                {"temp": 300, "time": 30, "end_flag": -121}
            ]
            program_id = self.database_manager.add_program(
                name="集成测试程序",
                segments=segments,
                description="用于集成测试的程序",
                material_id=material_id
            )
            print(f"添加程序成功: ID {program_id}")
            return program_id
        except Exception as e:
            print(f"添加程序失败: {e}")
            return None
    
    def test_program_management(self, program_id):
        """测试程序管理"""
        print("\n=== 测试程序管理 ===")
        
        if program_id:
            # 测试程序写入
            success = self.program_manager.write_program_to_instrument(1, program_id)
            print(f"程序写入结果: {'成功' if success else '失败'}")
            
            if success:
                # 测试程序启动
                start_success = self.program_manager.start_program(1)
                print(f"程序启动结果: {'成功' if start_success else '失败'}")
                
                # 获取程序状态
                status = self.program_manager.get_program_status(1)
                if status:
                    print(f"程序状态: {status['status']}")
    
    def test_monitoring_system(self):
        """测试监控系统"""
        print("\n=== 测试监控系统 ===")
        
        # 启动测试数据生成
        self.test_data_timer.start(1000)  # 每秒生成一次数据
        print("开始生成测试数据...")
        
        # 运行5秒
        QTimer.singleShot(5000, self._stop_test_data)
    
    def _stop_test_data(self):
        """停止测试数据生成"""
        self.test_data_timer.stop()
        print("停止生成测试数据")
        
        # 显示最终统计
        stats = self.monitoring_manager.get_statistics()
        print(f"最终统计: {stats}")
        
        # 退出应用
        QTimer.singleShot(1000, self.app.quit)
    
    def run_tests(self):
        """运行所有测试"""
        print("开始集成测试...")
        
        # 测试数据库操作
        program_id = self.test_database_operations()
        
        # 测试程序管理
        self.test_program_management(program_id)
        
        # 测试监控系统
        self.test_monitoring_system()
        
        # 运行应用
        return self.app.exec_()
    
    def cleanup(self):
        """清理测试环境"""
        test_files = [
            "test_integration_config.json",
            "test_integration.db"
        ]
        
        for file in test_files:
            if os.path.exists(file):
                try:
                    os.remove(file)
                    print(f"删除测试文件: {file}")
                except Exception as e:
                    print(f"删除文件失败 {file}: {e}")


def main():
    """主函数"""
    test = IntegrationTest()
    
    try:
        result = test.run_tests()
        print(f"\n集成测试完成，退出码: {result}")
        
    except Exception as e:
        print(f"集成测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        test.cleanup()


if __name__ == "__main__":
    main()
