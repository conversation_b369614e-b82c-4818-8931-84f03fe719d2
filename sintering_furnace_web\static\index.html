<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>8通道烧结炉控制系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-thermometer-half"></i>
                8通道烧结炉控制系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" data-tab="monitoring">实时监控</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-tab="programs">程序管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-tab="history">历史数据</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-tab="config">系统配置</a>
                    </li>
                </ul>
                <div class="navbar-nav">
                    <span class="navbar-text me-3">
                        <i class="bi bi-wifi" id="connection-status"></i>
                        <span id="connection-text">连接中...</span>
                    </span>
                    <span class="navbar-text">
                        <i class="bi bi-clock"></i>
                        <span id="current-time"></span>
                    </span>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="container-fluid mt-3">
        <!-- 实时监控页面 -->
        <div id="monitoring-tab" class="tab-content active">
            <div class="row mb-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="bi bi-activity"></i>
                                实时监控
                            </h5>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-success btn-sm" id="start-all-btn">
                                    <i class="bi bi-play-fill"></i> 全部启动
                                </button>
                                <button type="button" class="btn btn-warning btn-sm" id="pause-all-btn">
                                    <i class="bi bi-pause-fill"></i> 全部暂停
                                </button>
                                <button type="button" class="btn btn-danger btn-sm" id="stop-all-btn">
                                    <i class="bi bi-stop-fill"></i> 全部停止
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 通道监控网格 -->
                            <div class="row" id="channels-grid">
                                <!-- 通道卡片将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 实时图表 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-graph-up"></i>
                                实时温度曲线
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="temperature-chart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 程序管理页面 -->
        <div id="programs-tab" class="tab-content">
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">程序列表</h5>
                            <button type="button" class="btn btn-primary btn-sm" id="new-program-btn">
                                <i class="bi bi-plus"></i> 新建
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="list-group" id="programs-list">
                                <!-- 程序列表将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">程序编辑器</h5>
                        </div>
                        <div class="card-body">
                            <div id="program-editor">
                                <form id="program-form">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="program-name" class="form-label">程序名称</label>
                                            <input type="text" class="form-control" id="program-name" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="program-description" class="form-label">程序描述</label>
                                            <input type="text" class="form-control" id="program-description">
                                        </div>
                                    </div>
                                    
                                    <h6>程序段设置</h6>
                                    <div id="program-segments">
                                        <!-- 程序段将通过JavaScript动态生成 -->
                                    </div>
                                    
                                    <div class="mt-3">
                                        <button type="button" class="btn btn-secondary" id="add-segment-btn">
                                            <i class="bi bi-plus"></i> 添加段
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-save"></i> 保存程序
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 历史数据页面 -->
        <div id="history-tab" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-clock-history"></i>
                        历史数据查询
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label for="history-channel" class="form-label">选择通道</label>
                            <select class="form-select" id="history-channel">
                                <option value="all">全部通道</option>
                                <option value="1">通道1</option>
                                <option value="2">通道2</option>
                                <option value="3">通道3</option>
                                <option value="4">通道4</option>
                                <option value="5">通道5</option>
                                <option value="6">通道6</option>
                                <option value="7">通道7</option>
                                <option value="8">通道8</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="history-start-date" class="form-label">开始时间</label>
                            <input type="datetime-local" class="form-control" id="history-start-date">
                        </div>
                        <div class="col-md-3">
                            <label for="history-end-date" class="form-label">结束时间</label>
                            <input type="datetime-local" class="form-control" id="history-end-date">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-primary" id="query-history-btn">
                                    <i class="bi bi-search"></i> 查询
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <canvas id="history-chart" width="400" height="300"></canvas>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <button type="button" class="btn btn-success" id="export-csv-btn">
                                <i class="bi bi-file-earmark-spreadsheet"></i> 导出CSV
                            </button>
                            <button type="button" class="btn btn-success" id="export-excel-btn">
                                <i class="bi bi-file-earmark-excel"></i> 导出Excel
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统配置页面 -->
        <div id="config-tab" class="tab-content">
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">串口配置</h5>
                        </div>
                        <div class="card-body">
                            <form id="serial-config-form">
                                <div class="mb-3">
                                    <label for="serial-port" class="form-label">串口号</label>
                                    <select class="form-select" id="serial-port">
                                        <option value="COM1">COM1</option>
                                        <option value="COM2">COM2</option>
                                        <option value="COM3">COM3</option>
                                        <option value="COM4">COM4</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="baud-rate" class="form-label">波特率</label>
                                    <select class="form-select" id="baud-rate">
                                        <option value="9600">9600</option>
                                        <option value="19200">19200</option>
                                        <option value="38400">38400</option>
                                        <option value="57600">57600</option>
                                        <option value="115200">115200</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-save"></i> 保存配置
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">通道配置</h5>
                        </div>
                        <div class="card-body">
                            <div id="channel-config">
                                <!-- 通道配置将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div class="modal fade" id="alertModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">系统提示</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="alert-message">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">确定</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
