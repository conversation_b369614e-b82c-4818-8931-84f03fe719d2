#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
8通道烧结炉控制系统 - Web版本启动脚本
"""

import os
import sys
import json
import logging
import uvicorn
from pathlib import Path

def setup_logging():
    """设置日志系统"""
    # 创建日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/web_app.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def load_config():
    """加载配置文件"""
    config_path = Path("config/web_config.json")
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    else:
        # 返回默认配置
        return {
            "server": {
                "host": "0.0.0.0",
                "port": 8000,
                "debug": False,
                "reload": False
            }
        }

def check_dependencies():
    """检查依赖项"""
    required_dirs = [
        "static",
        "static/css",
        "static/js",
        "config",
        "core",
        "logs"
    ]
    
    for dir_path in required_dirs:
        Path(dir_path).mkdir(exist_ok=True)
    
    # 检查静态文件
    required_files = [
        "static/index.html",
        "static/css/style.css",
        "static/js/app.js"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"警告: 以下文件缺失: {missing_files}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("8通道烧结炉控制系统 - Web版本")
    print("版本: 2.0")
    print("=" * 60)
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # 检查依赖
    if not check_dependencies():
        logger.warning("部分文件缺失，可能影响系统功能")
    
    # 加载配置
    config = load_config()
    server_config = config.get("server", {})
    
    # 获取服务器配置
    host = server_config.get("host", "0.0.0.0")
    port = server_config.get("port", 8000)
    debug = server_config.get("debug", False)
    reload = server_config.get("reload", False)
    
    logger.info(f"启动Web服务器: http://{host}:{port}")
    logger.info(f"调试模式: {debug}")
    logger.info(f"自动重载: {reload}")
    
    try:
        # 启动服务器
        uvicorn.run(
            "app:app",
            host=host,
            port=port,
            log_level="info",
            reload=reload,
            access_log=True
        )
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
