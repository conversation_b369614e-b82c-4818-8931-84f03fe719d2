#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web版本监控管理器
适配原有监控管理器到Web架构，支持异步操作
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass

# 导入原有模块
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'sintering_furnace_control'))

from src.core.monitoring_manager import MonitoringManager as BaseMonitoringManager
from src.core.monitoring_manager import AlarmManager


@dataclass
class ChannelStatus:
    """通道状态数据类"""
    channel: int
    pv: float  # 过程值
    sv: float  # 设定值
    mv: float  # 输出值
    status: str  # 运行状态
    timestamp: datetime
    alarms: List[str] = None


class WebMonitoringManager:
    """Web版本监控管理器"""
    
    def __init__(self, serial_manager, database_manager):
        """
        初始化Web监控管理器
        
        Args:
            serial_manager: 串口管理器
            database_manager: 数据库管理器
        """
        self.logger = logging.getLogger(__name__)
        self.serial_manager = serial_manager
        self.database_manager = database_manager
        
        # 初始化基础监控管理器
        self.base_manager = BaseMonitoringManager(serial_manager, database_manager)
        
        # Web特有属性
        self.websocket_connections = []
        self.monitoring_task = None
        self.is_monitoring = False
        
        # 数据缓存
        self.channel_data_cache = {}
        self.alarm_cache = {}
        
        self.logger.info("Web监控管理器初始化完成")
    
    async def start_monitoring(self):
        """启动异步监控"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        self.logger.info("开始Web监控")
    
    async def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        self.logger.info("停止Web监控")
    
    async def _monitoring_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 获取所有通道数据
                await self._update_all_channels()
                
                # 检查报警
                await self._check_alarms()
                
                # 广播数据到WebSocket连接
                await self._broadcast_data()
                
                # 记录数据到数据库
                await self._log_data()
                
            except Exception as e:
                self.logger.error(f"监控循环错误: {e}")
            
            await asyncio.sleep(1)  # 每秒更新一次
    
    async def _update_all_channels(self):
        """更新所有通道数据"""
        for channel in range(1, 9):
            try:
                # 使用原有的监控管理器获取数据
                data = self.base_manager.get_channel_data(channel)
                if data:
                    channel_status = ChannelStatus(
                        channel=channel,
                        pv=data.get('pv', 0.0),
                        sv=data.get('sv', 0.0),
                        mv=data.get('mv', 0.0),
                        status=data.get('status', 'UNKNOWN'),
                        timestamp=datetime.now(),
                        alarms=data.get('alarms', [])
                    )
                    self.channel_data_cache[channel] = channel_status
            except Exception as e:
                self.logger.error(f"更新通道{channel}数据失败: {e}")
    
    async def _check_alarms(self):
        """检查报警状态"""
        try:
            # 使用原有的报警管理器
            for channel, status in self.channel_data_cache.items():
                alarms = self.base_manager.alarm_manager.check_alarms(
                    channel, status.pv, status.sv, status.mv, status.status
                )
                if alarms:
                    self.alarm_cache[channel] = alarms
                    # 记录报警到数据库
                    for alarm in alarms:
                        self.database_manager.log_alarm(
                            channel, alarm['level'], alarm['message']
                        )
        except Exception as e:
            self.logger.error(f"检查报警失败: {e}")
    
    async def _broadcast_data(self):
        """广播数据到WebSocket连接"""
        if not self.websocket_connections:
            return
        
        try:
            # 准备广播数据
            broadcast_data = {
                "type": "monitoring_update",
                "timestamp": datetime.now().isoformat(),
                "channels": []
            }
            
            for channel, status in self.channel_data_cache.items():
                channel_data = {
                    "channel": channel,
                    "pv": status.pv,
                    "sv": status.sv,
                    "mv": status.mv,
                    "status": status.status,
                    "timestamp": status.timestamp.isoformat(),
                    "alarms": self.alarm_cache.get(channel, [])
                }
                broadcast_data["channels"].append(channel_data)
            
            # 发送到所有WebSocket连接
            message = json.dumps(broadcast_data)
            disconnected = []
            
            for websocket in self.websocket_connections:
                try:
                    await websocket.send_text(message)
                except:
                    disconnected.append(websocket)
            
            # 移除断开的连接
            for ws in disconnected:
                self.websocket_connections.remove(ws)
                
        except Exception as e:
            self.logger.error(f"广播数据失败: {e}")
    
    async def _log_data(self):
        """记录数据到数据库"""
        try:
            for channel, status in self.channel_data_cache.items():
                self.database_manager.log_data(
                    channel=channel,
                    pv=status.pv,
                    sv=status.sv,
                    mv=status.mv,
                    status=status.status,
                    timestamp=status.timestamp
                )
        except Exception as e:
            self.logger.error(f"记录数据失败: {e}")
    
    def add_websocket_connection(self, websocket):
        """添加WebSocket连接"""
        self.websocket_connections.append(websocket)
        self.logger.info(f"添加WebSocket连接，当前连接数: {len(self.websocket_connections)}")
    
    def remove_websocket_connection(self, websocket):
        """移除WebSocket连接"""
        if websocket in self.websocket_connections:
            self.websocket_connections.remove(websocket)
            self.logger.info(f"移除WebSocket连接，当前连接数: {len(self.websocket_connections)}")
    
    def get_channel_data(self, channel: int) -> Optional[Dict]:
        """获取指定通道数据"""
        if channel in self.channel_data_cache:
            status = self.channel_data_cache[channel]
            return {
                "channel": status.channel,
                "pv": status.pv,
                "sv": status.sv,
                "mv": status.mv,
                "status": status.status,
                "timestamp": status.timestamp.isoformat(),
                "alarms": self.alarm_cache.get(channel, [])
            }
        return None
    
    def get_all_channels_data(self) -> List[Dict]:
        """获取所有通道数据"""
        channels_data = []
        for channel in range(1, 9):
            data = self.get_channel_data(channel)
            if data:
                channels_data.append(data)
        return channels_data
    
    async def get_historical_data(self, channel: int, start_time: datetime, end_time: datetime) -> List[Dict]:
        """获取历史数据"""
        try:
            # 使用数据库管理器获取历史数据
            return self.database_manager.get_historical_data(channel, start_time, end_time)
        except Exception as e:
            self.logger.error(f"获取历史数据失败: {e}")
            return []
    
    async def export_data(self, channels: List[int], start_time: datetime, end_time: datetime, format: str = 'csv') -> str:
        """导出数据"""
        try:
            # 使用原有的导出功能
            return self.base_manager.export_data(channels, start_time, end_time, format)
        except Exception as e:
            self.logger.error(f"导出数据失败: {e}")
            raise
