#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通道控件
显示单个通道的实时数据和控制按钮
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QPushButton, QGroupBox, QProgressBar,
                            QFrame, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QColor


class ChannelWidget(QWidget):
    """通道控件类"""
    
    # 信号定义
    control_command_requested = pyqtSignal(int, str)  # 通道号, 命令
    setpoint_change_requested = pyqtSignal(int, float)  # 通道号, 设定值
    
    def __init__(self, channel_number):
        """
        初始化通道控件
        
        Args:
            channel_number: 通道号 (1-8)
        """
        super().__init__()
        self.channel_number = channel_number
        self.current_data = {}
        
        self._init_ui()
        self._init_styles()
    
    def _init_ui(self):
        """初始化用户界面"""
        # 设置控件大小策略
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.setFixedHeight(200)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建分组框
        group_box = QGroupBox(f"通道 {self.channel_number}")
        group_box.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid gray;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        main_layout.addWidget(group_box)
        
        # 分组框内布局
        group_layout = QVBoxLayout(group_box)
        
        # 数据显示区域
        self._create_data_display(group_layout)
        
        # 控制按钮区域
        self._create_control_buttons(group_layout)
    
    def _create_data_display(self, parent_layout):
        """创建数据显示区域"""
        # 数据网格布局
        data_layout = QGridLayout()
        parent_layout.addLayout(data_layout)
        
        # PV (当前值)
        pv_label = QLabel("PV:")
        pv_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.pv_value = QLabel("0.0°C")
        self.pv_value.setAlignment(Qt.AlignCenter)
        self.pv_value.setStyleSheet("""
            QLabel {
                background-color: #E8F4FD;
                border: 1px solid #B0D4F1;
                border-radius: 3px;
                padding: 2px;
                font-size: 14px;
                font-weight: bold;
                color: #2E86AB;
            }
        """)
        data_layout.addWidget(pv_label, 0, 0)
        data_layout.addWidget(self.pv_value, 0, 1)
        
        # SV (设定值)
        sv_label = QLabel("SV:")
        sv_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.sv_value = QLabel("0.0°C")
        self.sv_value.setAlignment(Qt.AlignCenter)
        self.sv_value.setStyleSheet("""
            QLabel {
                background-color: #FFF2E8;
                border: 1px solid #FFD4B0;
                border-radius: 3px;
                padding: 2px;
                font-size: 14px;
                font-weight: bold;
                color: #D2691E;
            }
        """)
        data_layout.addWidget(sv_label, 0, 2)
        data_layout.addWidget(self.sv_value, 0, 3)
        
        # MV (输出值)
        mv_label = QLabel("MV:")
        mv_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.mv_value = QLabel("0%")
        self.mv_value.setAlignment(Qt.AlignCenter)
        self.mv_value.setStyleSheet("""
            QLabel {
                background-color: #F0F8E8;
                border: 1px solid #C8E6C9;
                border-radius: 3px;
                padding: 2px;
                font-size: 14px;
                font-weight: bold;
                color: #388E3C;
            }
        """)
        data_layout.addWidget(mv_label, 1, 0)
        data_layout.addWidget(self.mv_value, 1, 1)
        
        # 状态
        status_label = QLabel("状态:")
        status_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.status_value = QLabel("STOP")
        self.status_value.setAlignment(Qt.AlignCenter)
        self.status_value.setStyleSheet("""
            QLabel {
                background-color: #F5F5F5;
                border: 1px solid #CCCCCC;
                border-radius: 3px;
                padding: 2px;
                font-size: 12px;
                font-weight: bold;
            }
        """)
        data_layout.addWidget(status_label, 1, 2)
        data_layout.addWidget(self.status_value, 1, 3)
        
        # 输出进度条
        self.mv_progress = QProgressBar()
        self.mv_progress.setRange(0, 100)
        self.mv_progress.setValue(0)
        self.mv_progress.setTextVisible(True)
        self.mv_progress.setFormat("输出: %p%")
        data_layout.addWidget(self.mv_progress, 2, 0, 1, 4)
    
    def _create_control_buttons(self, parent_layout):
        """创建控制按钮区域"""
        button_layout = QHBoxLayout()
        parent_layout.addLayout(button_layout)
        
        # 启动按钮
        self.start_btn = QPushButton("启动")
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        self.start_btn.clicked.connect(lambda: self._send_control_command("START"))
        button_layout.addWidget(self.start_btn)
        
        # 停止按钮
        self.stop_btn = QPushButton("停止")
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:pressed {
                background-color: #c1170a;
            }
        """)
        self.stop_btn.clicked.connect(lambda: self._send_control_command("STOP"))
        button_layout.addWidget(self.stop_btn)
        
        # 暂停按钮
        self.hold_btn = QPushButton("暂停")
        self.hold_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e68900;
            }
            QPushButton:pressed {
                background-color: #cc7a00;
            }
        """)
        self.hold_btn.clicked.connect(lambda: self._send_control_command("HOLD"))
        button_layout.addWidget(self.hold_btn)
    
    def _init_styles(self):
        """初始化样式"""
        self.setStyleSheet("""
            QWidget {
                background-color: #FAFAFA;
            }
            QLabel {
                color: #333333;
            }
        """)
    
    def _send_control_command(self, command):
        """发送控制命令"""
        self.control_command_requested.emit(self.channel_number, command)
    
    def update_data(self, data):
        """
        更新显示数据
        
        Args:
            data: 数据字典，包含pv, sv, mv, status等
        """
        self.current_data = data
        
        # 更新PV值
        pv = data.get('pv', 0.0)
        self.pv_value.setText(f"{pv:.1f}°C")
        
        # 更新SV值
        sv = data.get('sv', 0.0)
        self.sv_value.setText(f"{sv:.1f}°C")
        
        # 更新MV值
        mv = data.get('mv', 0)
        self.mv_value.setText(f"{mv}%")
        self.mv_progress.setValue(int(mv))
        
        # 更新状态
        status = data.get('status', 'UNKNOWN')
        self.status_value.setText(status)
        
        # 根据状态更新状态标签颜色
        self._update_status_color(status)
    
    def _update_status_color(self, status):
        """根据状态更新颜色"""
        if status == "RUN":
            color = "#4CAF50"  # 绿色
            bg_color = "#E8F5E8"
        elif status == "STOP":
            color = "#757575"  # 灰色
            bg_color = "#F5F5F5"
        elif status == "HOLD":
            color = "#FF9800"  # 橙色
            bg_color = "#FFF3E0"
        elif status == "ERROR":
            color = "#F44336"  # 红色
            bg_color = "#FFEBEE"
        else:
            color = "#9E9E9E"  # 默认灰色
            bg_color = "#F5F5F5"
        
        self.status_value.setStyleSheet(f"""
            QLabel {{
                background-color: {bg_color};
                border: 1px solid {color};
                border-radius: 3px;
                padding: 2px;
                font-size: 12px;
                font-weight: bold;
                color: {color};
            }}
        """)
    
    def get_current_data(self):
        """获取当前数据"""
        return self.current_data.copy()
    
    def set_enabled_controls(self, enabled):
        """设置控制按钮是否可用"""
        self.start_btn.setEnabled(enabled)
        self.stop_btn.setEnabled(enabled)
        self.hold_btn.setEnabled(enabled)
