#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宇电AIBUS协议实现
支持宇电AI系列温控仪表的通讯协议
"""

import struct
import logging
from typing import Tuple, Optional, List, Dict, Any
from enum import Enum


class AIBUSCommand(Enum):
    """AIBUS命令类型"""
    READ = 0x82
    WRITE = 0x43


class AIBUSParameter(Enum):
    """AIBUS参数代号"""
    PV_SV = 0x00        # PV/SV值
    MANUAL_OUTPUT = 0x1A # 手动输出值
    RUN_STATUS = 0x1B    # 运行状态
    PROGRAM_SEGMENT = 0x50  # 程序段数据起始地址


class InstrumentStatus(Enum):
    """仪表运行状态"""
    STOP = 0
    RUN = 1
    HOLD = 2
    ERROR = 3


class AIBUSProtocol:
    """宇电AIBUS协议处理类"""
    
    def __init__(self):
        """初始化协议处理器"""
        self.logger = logging.getLogger(__name__)
    
    def calculate_checksum(self, addr: int, cmd: int, param: int, data_low: int = 0, data_high: int = 0) -> <PERSON><PERSON>[int, int]:
        """
        计算校验码
        
        Args:
            addr: 仪表地址
            cmd: 命令字节
            param: 参数代号
            data_low: 数据低字节
            data_high: 数据高字节
            
        Returns:
            Tuple[int, int]: (校验码低字节, 校验码高字节)
        """
        if cmd == AIBUSCommand.READ.value:
            # 读指令校验码 = 参数代号 * 256 + 82 + Addr
            checksum = param * 256 + 82 + addr
        else:
            # 写指令校验码 = 参数代号 * 256 + 67 + 数据值 + Addr
            data_value = (data_high << 8) | data_low
            checksum = param * 256 + 67 + data_value + addr
        
        # 分解为高低字节
        checksum_low = checksum & 0xFF
        checksum_high = (checksum >> 8) & 0xFF
        
        return checksum_low, checksum_high
    
    def create_read_command(self, addr: int, param: int) -> bytes:
        """
        创建读取指令
        
        Args:
            addr: 仪表地址 (1-8)
            param: 参数代号
            
        Returns:
            bytes: 指令字节序列
        """
        # 指令格式: [Addr+80H] [82H] [参数代号] [00] [00] [校验码低] [校验码高]
        addr_byte = addr + 0x80
        cmd_byte = AIBUSCommand.READ.value
        
        checksum_low, checksum_high = self.calculate_checksum(addr, cmd_byte, param)
        
        command = bytes([
            addr_byte,
            cmd_byte,
            param,
            0x00,
            0x00,
            checksum_low,
            checksum_high
        ])
        
        self.logger.debug(f"创建读取指令 - 地址:{addr}, 参数:{param:02X}, 指令:{command.hex()}")
        return command
    
    def create_write_command(self, addr: int, param: int, value: int) -> bytes:
        """
        创建写入指令
        
        Args:
            addr: 仪表地址 (1-8)
            param: 参数代号
            value: 写入值
            
        Returns:
            bytes: 指令字节序列
        """
        # 指令格式: [Addr+80H] [43H] [参数代号] [数据低] [数据高] [校验码低] [校验码高]
        addr_byte = addr + 0x80
        cmd_byte = AIBUSCommand.WRITE.value
        
        data_low = value & 0xFF
        data_high = (value >> 8) & 0xFF
        
        checksum_low, checksum_high = self.calculate_checksum(addr, cmd_byte, param, data_low, data_high)
        
        command = bytes([
            addr_byte,
            cmd_byte,
            param,
            data_low,
            data_high,
            checksum_low,
            checksum_high
        ])
        
        self.logger.debug(f"创建写入指令 - 地址:{addr}, 参数:{param:02X}, 值:{value}, 指令:{command.hex()}")
        return command
    
    def parse_response(self, response: bytes) -> Optional[Dict[str, Any]]:
        """
        解析仪表响应数据
        
        Args:
            response: 响应字节序列
            
        Returns:
            Dict[str, Any]: 解析后的数据，包含PV、SV、MV、状态等
        """
        if len(response) != 10:
            self.logger.error(f"响应数据长度错误: {len(response)}, 期望: 10")
            return None
        
        try:
            # 响应格式: 10字节数据：PV(2)、SV(2)、MV(1)+状态(1)、参数值(2)、校验码(2)
            pv_raw = struct.unpack('<H', response[0:2])[0]  # 小端序
            sv_raw = struct.unpack('<H', response[2:4])[0]
            mv_status = response[4]
            status_byte = response[5]
            param_value = struct.unpack('<H', response[6:8])[0]
            checksum = struct.unpack('<H', response[8:10])[0]
            
            # 解析MV和状态
            mv = mv_status  # MV值 (0-100%)
            status = self._parse_status(status_byte)
            
            # 根据dPt参数处理小数点 (这里假设dPt=1，即一位小数)
            pv = pv_raw / 10.0
            sv = sv_raw / 10.0
            
            result = {
                'pv': pv,           # 当前值
                'sv': sv,           # 设定值
                'mv': mv,           # 输出值
                'status': status,   # 运行状态
                'param_value': param_value,  # 参数值
                'checksum': checksum,        # 校验码
                'raw_data': response.hex()   # 原始数据
            }
            
            self.logger.debug(f"解析响应数据: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"解析响应数据失败: {e}")
            return None
    
    def _parse_status(self, status_byte: int) -> str:
        """
        解析状态字节
        
        Args:
            status_byte: 状态字节
            
        Returns:
            str: 状态描述
        """
        # 根据宇电协议文档解析状态
        if status_byte & 0x01:
            return "RUN"
        elif status_byte & 0x02:
            return "HOLD"
        elif status_byte & 0x04:
            return "ERROR"
        else:
            return "STOP"
    
    def create_control_command(self, addr: int, command: str) -> Optional[bytes]:
        """
        创建控制指令
        
        Args:
            addr: 仪表地址
            command: 控制命令 ("START", "STOP", "HOLD")
            
        Returns:
            bytes: 控制指令字节序列
        """
        # 控制命令参数代号 (根据实际协议文档调整)
        control_params = {
            "START": 0x1C,  # 启动
            "STOP": 0x1D,   # 停止
            "HOLD": 0x1E    # 暂停
        }
        
        if command not in control_params:
            self.logger.error(f"不支持的控制命令: {command}")
            return None
        
        param = control_params[command]
        value = 1  # 执行命令
        
        return self.create_write_command(addr, param, value)
    
    def validate_response_checksum(self, response: bytes) -> bool:
        """
        验证响应数据的校验码
        
        Args:
            response: 响应字节序列
            
        Returns:
            bool: 校验码正确返回True
        """
        if len(response) != 10:
            return False
        
        try:
            # 计算期望的校验码
            data_sum = sum(response[0:8])
            expected_checksum = data_sum & 0xFFFF
            
            # 获取实际校验码
            actual_checksum = struct.unpack('<H', response[8:10])[0]
            
            return expected_checksum == actual_checksum
            
        except Exception as e:
            self.logger.error(f"校验码验证失败: {e}")
            return False


class ProgramSegment:
    """程序段数据结构"""

    def __init__(self, target_temp: float = 0.0, time_minutes: int = 0, end_flag: int = 0):
        """
        初始化程序段

        Args:
            target_temp: 目标温度
            time_minutes: 时间(分钟)
            end_flag: 结束标志 (-121表示程序结束)
        """
        self.target_temp = target_temp
        self.time_minutes = time_minutes
        self.end_flag = end_flag

    def to_bytes(self) -> bytes:
        """转换为字节数据"""
        # 温度值需要根据dPt参数调整 (假设dPt=1)
        temp_raw = int(self.target_temp * 10)

        # 程序段数据格式: [温度低][温度高][时间低][时间高][结束标志低][结束标志高]
        data = struct.pack('<HHH', temp_raw, self.time_minutes, self.end_flag)
        return data

    @classmethod
    def from_bytes(cls, data: bytes) -> 'ProgramSegment':
        """从字节数据创建程序段"""
        if len(data) < 6:
            raise ValueError("程序段数据长度不足")

        temp_raw, time_minutes, end_flag = struct.unpack('<HHH', data[:6])
        target_temp = temp_raw / 10.0  # 根据dPt参数调整

        return cls(target_temp, time_minutes, end_flag)

    def __str__(self):
        return f"ProgramSegment(temp={self.target_temp}°C, time={self.time_minutes}min, end={self.end_flag})"
