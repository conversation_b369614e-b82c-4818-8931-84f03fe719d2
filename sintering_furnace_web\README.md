# 8通道烧结炉控制系统 - Web版本

## 项目简介

这是8通道烧结炉控制系统的Web版本，将原有的PyQt5桌面应用重构为基于Web的控制界面。系统支持通过浏览器进行远程监控和控制，具有响应式设计，支持多设备访问。

## 技术栈

### 后端
- **FastAPI**: 现代化的Python Web框架
- **WebSocket**: 实时数据传输
- **SQLite**: 数据存储
- **Uvicorn**: ASGI服务器

### 前端
- **HTML5/CSS3/JavaScript**: 原生Web技术
- **Bootstrap 5**: 响应式UI框架
- **Chart.js**: 数据可视化
- **WebSocket API**: 实时通信

## 功能特性

### 实时监控
- 8通道温度实时显示
- 实时温度曲线图
- 设备状态监控
- 报警管理

### 程序管理
- 控温程序编辑
- 程序库管理
- 程序执行控制

### 历史数据
- 历史数据查询
- 数据导出(CSV/Excel)
- 趋势分析

### 系统配置
- 串口配置
- 通道配置
- 参数设置

## 安装部署

### 环境要求
- Python 3.7+
- 现代浏览器(Chrome/Firefox/Edge)
- RS485转换器
- 宇电AI系列温控仪表

### 安装步骤

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **配置系统**
编辑 `config/web_config.json` 文件，设置串口参数和通道配置。

3. **启动服务**
```bash
python run_web.py
```

4. **访问界面**
打开浏览器访问: http://localhost:8000

## 目录结构

```
sintering_furnace_web/
├── app.py                 # 主应用程序
├── run_web.py            # 启动脚本
├── requirements.txt      # 依赖列表
├── README.md            # 说明文档
├── config/              # 配置文件
│   └── web_config.json
├── core/                # 核心业务逻辑
│   ├── web_monitoring_manager.py
│   └── web_program_manager.py
├── static/              # 静态文件
│   ├── index.html
│   ├── css/
│   │   └── style.css
│   └── js/
│       └── app.js
└── logs/               # 日志文件
```

## API接口

### 设备控制
- `GET /api/channels` - 获取所有通道状态
- `GET /api/channels/{id}` - 获取指定通道状态
- `POST /api/channels/{id}/control` - 控制指定通道

### 程序管理
- `GET /api/programs` - 获取程序列表
- `POST /api/programs` - 创建新程序
- `PUT /api/programs/{id}` - 更新程序
- `DELETE /api/programs/{id}` - 删除程序

### 历史数据
- `GET /api/history` - 查询历史数据
- `GET /api/export` - 导出数据

### 系统配置
- `GET /api/config` - 获取系统配置
- `POST /api/config` - 更新系统配置

### WebSocket
- `WS /ws` - 实时数据推送

## 使用说明

### 实时监控
1. 打开浏览器访问系统
2. 在"实时监控"页面查看各通道状态
3. 使用控制按钮启动/停止/暂停设备
4. 观察实时温度曲线

### 程序管理
1. 切换到"程序管理"页面
2. 点击"新建"创建控温程序
3. 设置程序段参数
4. 保存并应用到通道

### 历史数据
1. 在"历史数据"页面选择查询条件
2. 点击"查询"显示历史曲线
3. 使用"导出"功能保存数据

### 系统配置
1. 在"系统配置"页面设置串口参数
2. 配置各通道地址和启用状态
3. 保存配置并重启服务

## 注意事项

1. **硬件连接**: 确保RS485转换器正确连接
2. **串口权限**: 可能需要管理员权限访问串口
3. **防火墙**: 确保8000端口未被阻止
4. **浏览器兼容**: 建议使用现代浏览器
5. **网络安全**: 生产环境请配置HTTPS和访问控制

## 故障排除

### 常见问题

1. **无法连接设备**
   - 检查串口配置
   - 确认设备地址
   - 检查硬件连接

2. **WebSocket连接失败**
   - 检查防火墙设置
   - 确认服务器正常运行
   - 刷新浏览器页面

3. **数据不更新**
   - 检查设备通信状态
   - 查看服务器日志
   - 重启服务

### 日志查看
```bash
tail -f logs/web_app.log
```

## 开发说明

### 添加新功能
1. 后端: 在 `app.py` 中添加API接口
2. 前端: 在 `static/js/app.js` 中添加功能
3. 样式: 在 `static/css/style.css` 中添加样式

### 调试模式
在 `config/web_config.json` 中设置:
```json
{
    "server": {
        "debug": true,
        "reload": true
    }
}
```

## 版本历史

- **v2.0** - Web版本重构
  - 基于FastAPI的Web架构
  - 响应式前端界面
  - WebSocket实时通信
  - 完整的API接口

- **v1.0** - 桌面版本
  - PyQt5桌面应用
  - 本地数据库存储
  - 串口通信协议

## 许可证

本项目仅供学习和研究使用。

## 联系方式

如有问题或建议，请联系开发团队。
