#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口通讯管理器
负责与8台宇电AI系列温控仪表的串口通讯
"""

import time
import threading
import logging
from typing import Dict, List, Optional, Callable, Any
import serial
import serial.tools.list_ports
from PyQt5.QtCore import QObject, pyqtSignal, QTimer

from .aibus_protocol import AIBUSProtocol, AIBUSParameter, ProgramSegment


class SerialManager(QObject):
    """串口通讯管理器"""
    
    # 信号定义
    data_received = pyqtSignal(int, dict)  # 通道号, 数据字典
    connection_status_changed = pyqtSignal(bool)  # 连接状态
    error_occurred = pyqtSignal(str)  # 错误信息
    
    def __init__(self, config_manager):
        """
        初始化串口管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        super().__init__()
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # 串口对象
        self.serial_port = None
        self.is_connected = False
        
        # 协议处理器
        self.protocol = AIBUSProtocol()
        
        # 通讯参数
        self.instrument_addresses = list(range(1, 9))  # 1-8
        self.sampling_interval = 1.0  # 采样间隔(秒)
        
        # 数据缓存
        self.latest_data = {}  # {channel: data_dict}
        
        # 定时器
        self.polling_timer = QTimer()
        self.polling_timer.timeout.connect(self._poll_instruments)
        
        # 线程锁
        self.comm_lock = threading.Lock()
        
        # 加载配置
        self._load_config()
    
    def _load_config(self):
        """加载串口配置"""
        serial_config = self.config_manager.get_serial_config()
        instrument_config = self.config_manager.get_instrument_config()
        
        self.serial_config = {
            'port': serial_config.get('port', 'COM1'),
            'baudrate': serial_config.get('baudrate', 9600),
            'bytesize': serial_config.get('bytesize', 8),
            'parity': serial_config.get('parity', 'N'),
            'stopbits': serial_config.get('stopbits', 1),
            'timeout': serial_config.get('timeout', 1.0)
        }
        
        self.instrument_addresses = instrument_config.get('addresses', list(range(1, 9)))
        self.sampling_interval = instrument_config.get('sampling_interval', 1.0)
    
    @staticmethod
    def get_available_ports() -> List[str]:
        """
        获取可用的串口列表
        
        Returns:
            List[str]: 可用串口列表
        """
        ports = []
        for port in serial.tools.list_ports.comports():
            ports.append(port.device)
        return ports
    
    def connect(self) -> bool:
        """
        连接串口
        
        Returns:
            bool: 连接成功返回True
        """
        try:
            if self.is_connected:
                self.logger.warning("串口已连接")
                return True
            
            # 创建串口对象
            self.serial_port = serial.Serial(
                port=self.serial_config['port'],
                baudrate=self.serial_config['baudrate'],
                bytesize=self.serial_config['bytesize'],
                parity=self.serial_config['parity'],
                stopbits=self.serial_config['stopbits'],
                timeout=self.serial_config['timeout']
            )
            
            if self.serial_port.is_open:
                self.is_connected = True
                self.connection_status_changed.emit(True)
                self.logger.info(f"串口连接成功: {self.serial_config['port']}")
                return True
            else:
                self.logger.error("串口打开失败")
                return False
                
        except Exception as e:
            self.logger.error(f"串口连接失败: {e}")
            self.error_occurred.emit(f"串口连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        try:
            self.stop_polling()
            
            if self.serial_port and self.serial_port.is_open:
                self.serial_port.close()
            
            self.is_connected = False
            self.connection_status_changed.emit(False)
            self.logger.info("串口已断开")
            
        except Exception as e:
            self.logger.error(f"断开串口失败: {e}")
    
    def start_polling(self):
        """开始轮询仪表数据"""
        if not self.is_connected:
            self.logger.warning("串口未连接，无法开始轮询")
            return
        
        interval_ms = int(self.sampling_interval * 1000)
        self.polling_timer.start(interval_ms)
        self.logger.info(f"开始轮询仪表数据，间隔: {self.sampling_interval}秒")
    
    def stop_polling(self):
        """停止轮询仪表数据"""
        self.polling_timer.stop()
        self.logger.info("停止轮询仪表数据")
    
    def _poll_instruments(self):
        """轮询所有仪表数据"""
        for addr in self.instrument_addresses:
            try:
                data = self.read_instrument_data(addr)
                if data:
                    self.latest_data[addr] = data
                    self.data_received.emit(addr, data)
            except Exception as e:
                self.logger.error(f"轮询通道{addr}数据失败: {e}")
    
    def read_instrument_data(self, addr: int) -> Optional[Dict[str, Any]]:
        """
        读取单个仪表数据
        
        Args:
            addr: 仪表地址
            
        Returns:
            Dict[str, Any]: 仪表数据
        """
        if not self.is_connected:
            return None
        
        with self.comm_lock:
            try:
                # 创建读取PV/SV指令
                command = self.protocol.create_read_command(addr, AIBUSParameter.PV_SV.value)
                
                # 发送指令
                self.serial_port.write(command)
                self.serial_port.flush()
                
                # 等待响应
                time.sleep(0.05)  # 50ms延时
                
                # 读取响应
                response = self.serial_port.read(10)
                
                if len(response) == 10:
                    # 解析响应数据
                    data = self.protocol.parse_response(response)
                    if data:
                        data['channel'] = addr
                        data['timestamp'] = time.time()
                        return data
                else:
                    self.logger.warning(f"通道{addr}响应数据长度错误: {len(response)}")
                    
            except Exception as e:
                self.logger.error(f"读取通道{addr}数据失败: {e}")
        
        return None
    
    def write_setpoint(self, addr: int, setpoint: float) -> bool:
        """
        写入设定值
        
        Args:
            addr: 仪表地址
            setpoint: 设定值
            
        Returns:
            bool: 写入成功返回True
        """
        if not self.is_connected:
            return False
        
        with self.comm_lock:
            try:
                # 转换设定值 (假设dPt=1)
                setpoint_raw = int(setpoint * 10)
                
                # 创建写入指令
                command = self.protocol.create_write_command(addr, AIBUSParameter.PV_SV.value, setpoint_raw)
                
                # 发送指令
                self.serial_port.write(command)
                self.serial_port.flush()
                
                self.logger.info(f"通道{addr}设定值写入: {setpoint}°C")
                return True
                
            except Exception as e:
                self.logger.error(f"写入通道{addr}设定值失败: {e}")
                return False
    
    def send_control_command(self, addr: int, command: str) -> bool:
        """
        发送控制命令
        
        Args:
            addr: 仪表地址
            command: 控制命令 ("START", "STOP", "HOLD")
            
        Returns:
            bool: 发送成功返回True
        """
        if not self.is_connected:
            return False
        
        with self.comm_lock:
            try:
                # 创建控制指令
                cmd_bytes = self.protocol.create_control_command(addr, command)
                if not cmd_bytes:
                    return False
                
                # 发送指令
                self.serial_port.write(cmd_bytes)
                self.serial_port.flush()
                
                self.logger.info(f"通道{addr}控制命令发送: {command}")
                return True
                
            except Exception as e:
                self.logger.error(f"发送通道{addr}控制命令失败: {e}")
                return False
    
    def get_latest_data(self, addr: int) -> Optional[Dict[str, Any]]:
        """
        获取最新数据
        
        Args:
            addr: 仪表地址
            
        Returns:
            Dict[str, Any]: 最新数据
        """
        return self.latest_data.get(addr)
    
    def get_all_latest_data(self) -> Dict[int, Dict[str, Any]]:
        """
        获取所有通道的最新数据
        
        Returns:
            Dict[int, Dict[str, Any]]: 所有通道数据
        """
        return self.latest_data.copy()
