#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
负责系统配置的加载、保存和管理
"""

import os
import json
import logging
from typing import Dict, Any, Optional


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_file: str = "config/settings.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config_dir = os.path.dirname(config_file)
        self.logger = logging.getLogger(__name__)
        
        # 默认配置
        self.default_config = {
            "serial": {
                "port": "COM1",
                "baudrate": 9600,
                "bytesize": 8,
                "parity": "N",
                "stopbits": 1,
                "timeout": 1.0
            },
            "instruments": {
                "count": 8,
                "addresses": list(range(1, 9)),
                "sampling_interval": 1.0
            },
            "database": {
                "path": "data/furnace_control.db"
            },
            "logging": {
                "level": "INFO",
                "log_dir": "logs",
                "max_log_files": 30
            },
            "ui": {
                "theme": "default",
                "language": "zh_CN",
                "window_size": [1200, 800],
                "window_position": [100, 100]
            },
            "data_export": {
                "export_dir": "exports",
                "csv_delimiter": ",",
                "datetime_format": "%Y-%m-%d %H:%M:%S"
            }
        }
        
        self.config = self.default_config.copy()
        self._ensure_directories()
        self.load_config()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            "data",
            "logs",
            "exports",
            "config"
        ]

        # 添加配置目录（如果不为空）
        if self.config_dir and self.config_dir.strip():
            directories.append(self.config_dir)

        for directory in directories:
            if directory and directory.strip() and not os.path.exists(directory):
                try:
                    os.makedirs(directory)
                    self.logger.info(f"创建目录: {directory}")
                except Exception as e:
                    self.logger.error(f"创建目录失败 {directory}: {e}")
    
    def load_config(self) -> bool:
        """
        加载配置文件
        
        Returns:
            bool: 加载成功返回True，否则返回False
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # 合并配置，保留默认值
                    self._merge_config(self.config, loaded_config)
                    self.logger.info(f"配置文件加载成功: {self.config_file}")
                    return True
            else:
                self.logger.info("配置文件不存在，使用默认配置")
                self.save_config()
                return True
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """
        保存配置文件
        
        Returns:
            bool: 保存成功返回True，否则返回False
        """
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            self.logger.info(f"配置文件保存成功: {self.config_file}")
            return True
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False
    
    def _merge_config(self, default: Dict[str, Any], loaded: Dict[str, Any]):
        """
        合并配置字典
        
        Args:
            default: 默认配置
            loaded: 加载的配置
        """
        for key, value in loaded.items():
            if key in default:
                if isinstance(value, dict) and isinstance(default[key], dict):
                    self._merge_config(default[key], value)
                else:
                    default[key] = value
    
    def get(self, key_path: str, default_value: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key_path: 配置键路径，如 "serial.port"
            default_value: 默认值
            
        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default_value
    
    def set(self, key_path: str, value: Any) -> bool:
        """
        设置配置值
        
        Args:
            key_path: 配置键路径，如 "serial.port"
            value: 配置值
            
        Returns:
            bool: 设置成功返回True，否则返回False
        """
        keys = key_path.split('.')
        config = self.config
        
        try:
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            config[keys[-1]] = value
            return True
        except Exception as e:
            self.logger.error(f"设置配置值失败: {e}")
            return False
    
    def get_serial_config(self) -> Dict[str, Any]:
        """获取串口配置"""
        return self.config.get("serial", {})
    
    def get_instrument_config(self) -> Dict[str, Any]:
        """获取仪表配置"""
        return self.config.get("instruments", {})
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self.config.get("database", {})
    
    def reset_to_default(self):
        """重置为默认配置"""
        self.config = self.default_config.copy()
        self.save_config()
        self.logger.info("配置已重置为默认值")
