#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置对话框
用于设置串口参数和其他系统配置
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QComboBox, QSpinBox, QDoubleSpinBox,
                            QPushButton, QGroupBox, QTabWidget, QWidget,
                            QLineEdit, QCheckBox, QMessageBox)
from PyQt5.QtCore import Qt
import serial.tools.list_ports


class ConfigDialog(QDialog):
    """配置对话框类"""
    
    def __init__(self, config_manager, parent=None):
        """
        初始化配置对话框
        
        Args:
            config_manager: 配置管理器实例
            parent: 父窗口
        """
        super().__init__(parent)
        self.config_manager = config_manager
        
        self._init_ui()
        self._load_config()
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("系统配置")
        self.setModal(True)
        self.resize(500, 400)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)
        
        # 串口配置选项卡
        self._create_serial_tab(tab_widget)
        
        # 仪表配置选项卡
        self._create_instrument_tab(tab_widget)
        
        # 数据库配置选项卡
        self._create_database_tab(tab_widget)
        
        # 按钮区域
        self._create_buttons(main_layout)
    
    def _create_serial_tab(self, tab_widget):
        """创建串口配置选项卡"""
        serial_widget = QWidget()
        tab_widget.addTab(serial_widget, "串口配置")
        
        layout = QVBoxLayout(serial_widget)
        
        # 串口参数组
        serial_group = QGroupBox("串口参数")
        layout.addWidget(serial_group)
        
        serial_layout = QGridLayout(serial_group)
        
        # 串口号
        serial_layout.addWidget(QLabel("串口号:"), 0, 0)
        self.port_combo = QComboBox()
        self._refresh_ports()
        serial_layout.addWidget(self.port_combo, 0, 1)
        
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self._refresh_ports)
        serial_layout.addWidget(refresh_btn, 0, 2)
        
        # 波特率
        serial_layout.addWidget(QLabel("波特率:"), 1, 0)
        self.baudrate_combo = QComboBox()
        self.baudrate_combo.addItems(["9600", "19200", "38400", "57600", "115200"])
        self.baudrate_combo.setEditable(True)
        serial_layout.addWidget(self.baudrate_combo, 1, 1)
        
        # 数据位
        serial_layout.addWidget(QLabel("数据位:"), 2, 0)
        self.bytesize_combo = QComboBox()
        self.bytesize_combo.addItems(["7", "8"])
        serial_layout.addWidget(self.bytesize_combo, 2, 1)
        
        # 校验位
        serial_layout.addWidget(QLabel("校验位:"), 3, 0)
        self.parity_combo = QComboBox()
        self.parity_combo.addItems(["N (无)", "E (偶)", "O (奇)"])
        serial_layout.addWidget(self.parity_combo, 3, 1)
        
        # 停止位
        serial_layout.addWidget(QLabel("停止位:"), 4, 0)
        self.stopbits_combo = QComboBox()
        self.stopbits_combo.addItems(["1", "2"])
        serial_layout.addWidget(self.stopbits_combo, 4, 1)
        
        # 超时时间
        serial_layout.addWidget(QLabel("超时时间(秒):"), 5, 0)
        self.timeout_spin = QDoubleSpinBox()
        self.timeout_spin.setRange(0.1, 10.0)
        self.timeout_spin.setSingleStep(0.1)
        self.timeout_spin.setDecimals(1)
        serial_layout.addWidget(self.timeout_spin, 5, 1)
        
        layout.addStretch()
    
    def _create_instrument_tab(self, tab_widget):
        """创建仪表配置选项卡"""
        instrument_widget = QWidget()
        tab_widget.addTab(instrument_widget, "仪表配置")
        
        layout = QVBoxLayout(instrument_widget)
        
        # 仪表参数组
        instrument_group = QGroupBox("仪表参数")
        layout.addWidget(instrument_group)
        
        instrument_layout = QGridLayout(instrument_group)
        
        # 仪表数量
        instrument_layout.addWidget(QLabel("仪表数量:"), 0, 0)
        self.count_spin = QSpinBox()
        self.count_spin.setRange(1, 8)
        instrument_layout.addWidget(self.count_spin, 0, 1)
        
        # 采样间隔
        instrument_layout.addWidget(QLabel("采样间隔(秒):"), 1, 0)
        self.interval_spin = QDoubleSpinBox()
        self.interval_spin.setRange(0.1, 60.0)
        self.interval_spin.setSingleStep(0.1)
        self.interval_spin.setDecimals(1)
        instrument_layout.addWidget(self.interval_spin, 1, 1)
        
        # 仪表地址设置
        addresses_group = QGroupBox("仪表地址")
        layout.addWidget(addresses_group)
        
        addresses_layout = QGridLayout(addresses_group)
        
        self.address_spins = []
        for i in range(8):
            label = QLabel(f"通道{i+1}地址:")
            addresses_layout.addWidget(label, i // 4, (i % 4) * 2)
            
            spin = QSpinBox()
            spin.setRange(1, 255)
            spin.setValue(i + 1)
            self.address_spins.append(spin)
            addresses_layout.addWidget(spin, i // 4, (i % 4) * 2 + 1)
        
        layout.addStretch()
    
    def _create_database_tab(self, tab_widget):
        """创建数据库配置选项卡"""
        database_widget = QWidget()
        tab_widget.addTab(database_widget, "数据库配置")
        
        layout = QVBoxLayout(database_widget)
        
        # 数据库参数组
        database_group = QGroupBox("数据库参数")
        layout.addWidget(database_group)
        
        database_layout = QGridLayout(database_group)
        
        # 数据库路径
        database_layout.addWidget(QLabel("数据库路径:"), 0, 0)
        self.db_path_edit = QLineEdit()
        database_layout.addWidget(self.db_path_edit, 0, 1)
        
        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self._browse_database_path)
        database_layout.addWidget(browse_btn, 0, 2)
        
        # 日志配置组
        log_group = QGroupBox("日志配置")
        layout.addWidget(log_group)
        
        log_layout = QGridLayout(log_group)
        
        # 日志级别
        log_layout.addWidget(QLabel("日志级别:"), 0, 0)
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        log_layout.addWidget(self.log_level_combo, 0, 1)
        
        # 最大日志文件数
        log_layout.addWidget(QLabel("最大日志文件数:"), 1, 0)
        self.max_log_files_spin = QSpinBox()
        self.max_log_files_spin.setRange(1, 100)
        log_layout.addWidget(self.max_log_files_spin, 1, 1)
        
        layout.addStretch()
    
    def _create_buttons(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        parent_layout.addLayout(button_layout)
        
        button_layout.addStretch()
        
        # 确定按钮
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self._save_config)
        button_layout.addWidget(ok_btn)
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        # 应用按钮
        apply_btn = QPushButton("应用")
        apply_btn.clicked.connect(self._apply_config)
        button_layout.addWidget(apply_btn)
    
    def _refresh_ports(self):
        """刷新串口列表"""
        self.port_combo.clear()
        ports = [port.device for port in serial.tools.list_ports.comports()]
        self.port_combo.addItems(ports)
    
    def _browse_database_path(self):
        """浏览数据库路径"""
        from PyQt5.QtWidgets import QFileDialog
        
        filename, _ = QFileDialog.getSaveFileName(
            self, "选择数据库文件", "", "SQLite files (*.db);;All files (*.*)"
        )
        
        if filename:
            self.db_path_edit.setText(filename)
    
    def _load_config(self):
        """加载配置到界面"""
        # 串口配置
        serial_config = self.config_manager.get_serial_config()
        
        # 设置串口号
        port = serial_config.get('port', 'COM1')
        index = self.port_combo.findText(port)
        if index >= 0:
            self.port_combo.setCurrentIndex(index)
        else:
            self.port_combo.setCurrentText(port)
        
        # 设置波特率
        self.baudrate_combo.setCurrentText(str(serial_config.get('baudrate', 9600)))
        
        # 设置数据位
        self.bytesize_combo.setCurrentText(str(serial_config.get('bytesize', 8)))
        
        # 设置校验位
        parity = serial_config.get('parity', 'N')
        parity_map = {'N': 'N (无)', 'E': 'E (偶)', 'O': 'O (奇)'}
        self.parity_combo.setCurrentText(parity_map.get(parity, 'N (无)'))
        
        # 设置停止位
        self.stopbits_combo.setCurrentText(str(serial_config.get('stopbits', 1)))
        
        # 设置超时时间
        self.timeout_spin.setValue(serial_config.get('timeout', 1.0))
        
        # 仪表配置
        instrument_config = self.config_manager.get_instrument_config()
        
        self.count_spin.setValue(instrument_config.get('count', 8))
        self.interval_spin.setValue(instrument_config.get('sampling_interval', 1.0))
        
        addresses = instrument_config.get('addresses', list(range(1, 9)))
        for i, addr in enumerate(addresses[:8]):
            if i < len(self.address_spins):
                self.address_spins[i].setValue(addr)
        
        # 数据库配置
        database_config = self.config_manager.get_database_config()
        self.db_path_edit.setText(database_config.get('path', 'data/furnace_control.db'))
        
        # 日志配置
        logging_config = self.config_manager.get('logging', {})
        self.log_level_combo.setCurrentText(logging_config.get('level', 'INFO'))
        self.max_log_files_spin.setValue(logging_config.get('max_log_files', 30))
    
    def _save_config(self):
        """保存配置"""
        if self._apply_config():
            self.accept()
    
    def _apply_config(self):
        """应用配置"""
        try:
            # 串口配置
            parity_map = {'N (无)': 'N', 'E (偶)': 'E', 'O (奇)': 'O'}
            parity_text = self.parity_combo.currentText()
            parity = parity_map.get(parity_text, 'N')
            
            self.config_manager.set('serial.port', self.port_combo.currentText())
            self.config_manager.set('serial.baudrate', int(self.baudrate_combo.currentText()))
            self.config_manager.set('serial.bytesize', int(self.bytesize_combo.currentText()))
            self.config_manager.set('serial.parity', parity)
            self.config_manager.set('serial.stopbits', int(self.stopbits_combo.currentText()))
            self.config_manager.set('serial.timeout', self.timeout_spin.value())
            
            # 仪表配置
            self.config_manager.set('instruments.count', self.count_spin.value())
            self.config_manager.set('instruments.sampling_interval', self.interval_spin.value())
            
            addresses = [spin.value() for spin in self.address_spins]
            self.config_manager.set('instruments.addresses', addresses)
            
            # 数据库配置
            self.config_manager.set('database.path', self.db_path_edit.text())
            
            # 日志配置
            self.config_manager.set('logging.level', self.log_level_combo.currentText())
            self.config_manager.set('logging.max_log_files', self.max_log_files_spin.value())
            
            # 保存配置文件
            self.config_manager.save_config()
            
            QMessageBox.information(self, "成功", "配置已保存")
            return True
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存配置失败: {e}")
            return False
