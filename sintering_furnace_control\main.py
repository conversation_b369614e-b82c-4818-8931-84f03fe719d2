#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
8通道烧结炉控制软件主程序
作者: AI Assistant
版本: 1.0
日期: 2025-09-04
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTranslator, QLocale
from src.ui.main_window import MainWindow
from src.core.config_manager import ConfigManager
from src.utils.logger import setup_logger


def main():
    """主程序入口"""
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("8通道烧结炉控制软件")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Industrial Control Systems")
    
    # 设置中文界面
    translator = QTranslator()
    locale = QLocale.system().name()
    if translator.load(f"translations/app_{locale}.qm"):
        app.installTranslator(translator)
    
    # 初始化日志系统
    logger = setup_logger()
    logger.info("应用程序启动")
    
    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 创建主窗口
        main_window = MainWindow(config_manager)
        main_window.show()
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"应用程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
