#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序启动脚本
启动8通道烧结炉控制软件
"""

import sys
import os
import logging

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont

from src.ui.main_window import MainWindow
from src.core.config_manager import ConfigManager
from src.utils.logger import setup_logger


def create_splash_screen():
    """创建启动画面"""
    # 创建一个简单的启动画面
    splash_pix = QPixmap(400, 300)
    splash_pix.fill(Qt.white)
    
    splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
    
    # 设置启动画面文本
    splash.showMessage(
        "8通道烧结炉控制软件\n正在启动...",
        Qt.AlignCenter | Qt.AlignBottom,
        Qt.black
    )
    
    return splash


def check_dependencies():
    """检查依赖库"""
    missing_deps = []
    
    try:
        import PyQt5
    except ImportError:
        missing_deps.append("PyQt5")
    
    try:
        import serial
    except ImportError:
        missing_deps.append("pyserial")
    
    try:
        import matplotlib
    except ImportError:
        missing_deps.append("matplotlib")
    
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    if missing_deps:
        return False, missing_deps
    
    return True, []


def main():
    """主函数"""
    # 检查依赖
    deps_ok, missing_deps = check_dependencies()
    if not deps_ok:
        print("缺少必要的依赖库:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\n请运行以下命令安装依赖:")
        print("pip install PyQt5 pyserial matplotlib numpy pandas")
        return 1
    
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("8通道烧结炉控制软件")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Industrial Control Systems")
    
    # 设置应用程序图标（如果有的话）
    # app.setWindowIcon(QIcon("icon.ico"))
    
    try:
        # 显示启动画面
        splash = create_splash_screen()
        splash.show()
        app.processEvents()
        
        # 初始化日志系统
        splash.showMessage("初始化日志系统...", Qt.AlignCenter | Qt.AlignBottom, Qt.black)
        app.processEvents()
        
        logger = setup_logger()
        logger.info("应用程序启动")
        
        # 初始化配置管理器
        splash.showMessage("加载配置...", Qt.AlignCenter | Qt.AlignBottom, Qt.black)
        app.processEvents()
        
        config_manager = ConfigManager()
        
        # 创建主窗口
        splash.showMessage("创建主界面...", Qt.AlignCenter | Qt.AlignBottom, Qt.black)
        app.processEvents()
        
        main_window = MainWindow(config_manager)
        
        # 连接通道控件的控制信号
        for i, channel_widget in enumerate(main_window.channel_widgets):
            channel_widget.control_command_requested.connect(
                lambda channel, command: main_window.serial_manager.send_control_command(channel, command)
            )
        
        # 隐藏启动画面并显示主窗口
        splash.showMessage("启动完成", Qt.AlignCenter | Qt.AlignBottom, Qt.black)
        app.processEvents()
        
        QTimer.singleShot(1000, splash.close)  # 1秒后关闭启动画面
        QTimer.singleShot(1000, main_window.show)  # 1秒后显示主窗口
        
        logger.info("主窗口创建完成")
        
        # 运行应用程序
        return app.exec_()
        
    except Exception as e:
        # 错误处理
        error_msg = f"应用程序启动失败: {e}"
        print(error_msg)
        
        if 'logger' in locals():
            logger.error(error_msg)
        
        # 显示错误对话框
        try:
            QMessageBox.critical(None, "启动错误", error_msg)
        except:
            pass
        
        return 1


if __name__ == "__main__":
    sys.exit(main())
