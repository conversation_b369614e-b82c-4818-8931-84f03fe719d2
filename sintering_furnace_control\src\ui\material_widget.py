#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
材料库管理控件
用于管理材料数据的界面组件
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QGroupBox, QMessageBox)
from PyQt5.QtCore import Qt


class MaterialWidget(QWidget):
    """材料库管理控件类"""
    
    def __init__(self, database_manager):
        """
        初始化材料库管理控件
        
        Args:
            database_manager: 数据库管理器实例
        """
        super().__init__()
        self.database_manager = database_manager
        self._init_ui()
        self._load_materials()
    
    def _init_ui(self):
        """初始化用户界面"""
        main_layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("材料库管理")
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; margin: 10px;")
        main_layout.addWidget(title_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        main_layout.addLayout(button_layout)
        
        new_btn = QPushButton("新建材料")
        new_btn.clicked.connect(self._new_material)
        button_layout.addWidget(new_btn)
        
        edit_btn = QPushButton("编辑材料")
        edit_btn.clicked.connect(self._edit_material)
        button_layout.addWidget(edit_btn)
        
        delete_btn = QPushButton("删除材料")
        delete_btn.clicked.connect(self._delete_material)
        button_layout.addWidget(delete_btn)
        
        button_layout.addStretch()
        
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self._load_materials)
        button_layout.addWidget(refresh_btn)
        
        # 材料列表表格
        self.material_table = QTableWidget()
        self.material_table.setColumnCount(5)
        self.material_table.setHorizontalHeaderLabels(["ID", "材料名称", "熔点(°C)", "烧结温度(°C)", "描述"])
        self.material_table.setSelectionBehavior(QTableWidget.SelectRows)
        main_layout.addWidget(self.material_table)
    
    def _load_materials(self):
        """加载材料列表"""
        materials = self.database_manager.get_materials()
        
        self.material_table.setRowCount(len(materials))
        
        for row, material in enumerate(materials):
            self.material_table.setItem(row, 0, QTableWidgetItem(str(material['id'])))
            self.material_table.setItem(row, 1, QTableWidgetItem(material['name']))
            self.material_table.setItem(row, 2, QTableWidgetItem(str(material.get('melting_point', ''))))
            self.material_table.setItem(row, 3, QTableWidgetItem(str(material.get('sintering_temp', ''))))
            self.material_table.setItem(row, 4, QTableWidgetItem(material.get('description', '')))
        
        self.material_table.resizeColumnsToContents()
    
    def _new_material(self):
        """新建材料"""
        QMessageBox.information(self, "提示", "新建材料功能待实现")
    
    def _edit_material(self):
        """编辑材料"""
        QMessageBox.information(self, "提示", "编辑材料功能待实现")
    
    def _delete_material(self):
        """删除材料"""
        current_row = self.material_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要删除的材料")
            return
        
        material_id = int(self.material_table.item(current_row, 0).text())
        material_name = self.material_table.item(current_row, 1).text()
        
        reply = QMessageBox.question(self, "确认删除", 
                                   f"确定要删除材料 '{material_name}' 吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            if self.database_manager.delete_material(material_id):
                QMessageBox.information(self, "成功", "材料删除成功")
                self._load_materials()
            else:
                QMessageBox.critical(self, "错误", "材料删除失败")
