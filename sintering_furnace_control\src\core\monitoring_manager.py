#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时监控管理器
负责协调8通道的实时数据监控、报警处理和数据记录
"""

import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from PyQt5.QtCore import QObject, pyqtSignal, QTimer

from ..utils.logger import DataLogger


class AlarmManager:
    """报警管理器"""
    
    def __init__(self):
        """初始化报警管理器"""
        self.logger = logging.getLogger(__name__)
        self.alarm_rules = {}  # {channel: {rule_name: rule_config}}
        self.active_alarms = {}  # {channel: [alarm_list]}
        
        # 默认报警规则
        self._init_default_rules()
    
    def _init_default_rules(self):
        """初始化默认报警规则"""
        default_rules = {
            'high_temp': {
                'name': '高温报警',
                'condition': lambda pv, sv, mv, status: pv > 1200,  # 超过1200°C
                'level': 'ERROR',
                'message': '温度过高'
            },
            'temp_deviation': {
                'name': '温度偏差报警',
                'condition': lambda pv, sv, mv, status: abs(pv - sv) > 50 and sv > 0,  # 偏差超过50°C
                'level': 'WARNING',
                'message': '温度偏差过大'
            },
            'sensor_error': {
                'name': '传感器故障',
                'condition': lambda pv, sv, mv, status: status == 'ERROR',
                'level': 'ERROR',
                'message': '传感器故障'
            },
            'communication_error': {
                'name': '通讯故障',
                'condition': lambda pv, sv, mv, status: pv == 0 and sv == 0 and mv == 0,
                'level': 'ERROR',
                'message': '通讯故障'
            }
        }
        
        # 为每个通道设置默认规则
        for channel in range(1, 9):
            self.alarm_rules[channel] = default_rules.copy()
            self.active_alarms[channel] = []
    
    def check_alarms(self, channel: int, pv: float, sv: float, mv: float, status: str) -> List[Dict]:
        """
        检查报警条件
        
        Args:
            channel: 通道号
            pv: 当前值
            sv: 设定值
            mv: 输出值
            status: 状态
            
        Returns:
            List[Dict]: 触发的报警列表
        """
        triggered_alarms = []
        
        if channel not in self.alarm_rules:
            return triggered_alarms
        
        for rule_name, rule_config in self.alarm_rules[channel].items():
            try:
                condition = rule_config['condition']
                if condition(pv, sv, mv, status):
                    alarm = {
                        'channel': channel,
                        'rule_name': rule_name,
                        'name': rule_config['name'],
                        'level': rule_config['level'],
                        'message': rule_config['message'],
                        'timestamp': datetime.now(),
                        'pv': pv,
                        'sv': sv,
                        'mv': mv,
                        'status': status
                    }
                    triggered_alarms.append(alarm)
                    
            except Exception as e:
                self.logger.error(f"检查报警规则失败 - 通道{channel}, 规则{rule_name}: {e}")
        
        return triggered_alarms
    
    def add_alarm_rule(self, channel: int, rule_name: str, condition: Callable, 
                      level: str = 'WARNING', message: str = ''):
        """
        添加报警规则
        
        Args:
            channel: 通道号
            rule_name: 规则名称
            condition: 报警条件函数
            level: 报警级别
            message: 报警消息
        """
        if channel not in self.alarm_rules:
            self.alarm_rules[channel] = {}
        
        self.alarm_rules[channel][rule_name] = {
            'name': rule_name,
            'condition': condition,
            'level': level,
            'message': message
        }
    
    def remove_alarm_rule(self, channel: int, rule_name: str):
        """移除报警规则"""
        if channel in self.alarm_rules and rule_name in self.alarm_rules[channel]:
            del self.alarm_rules[channel][rule_name]


class MonitoringManager(QObject):
    """实时监控管理器"""
    
    # 信号定义
    data_updated = pyqtSignal(int, dict)  # 通道号, 数据
    alarm_triggered = pyqtSignal(int, dict)  # 通道号, 报警信息
    statistics_updated = pyqtSignal(dict)  # 统计信息
    
    def __init__(self, config_manager, database_manager):
        """
        初始化监控管理器
        
        Args:
            config_manager: 配置管理器
            database_manager: 数据库管理器
        """
        super().__init__()
        self.config_manager = config_manager
        self.database_manager = database_manager
        self.logger = logging.getLogger(__name__)
        
        # 数据存储
        self.channel_data = {}  # {channel: latest_data}
        self.data_history = {}  # {channel: deque of historical data}
        
        # 报警管理器
        self.alarm_manager = AlarmManager()
        
        # 数据日志记录器
        self.data_logger = DataLogger()
        
        # 统计信息
        self.statistics = {
            'total_samples': 0,
            'active_channels': 0,
            'alarm_count': 0,
            'last_update': None
        }
        
        # 定时器
        self.statistics_timer = QTimer()
        self.statistics_timer.timeout.connect(self._update_statistics)
        self.statistics_timer.start(5000)  # 每5秒更新统计信息
        
        # 初始化历史数据存储
        from collections import deque
        for channel in range(1, 9):
            self.data_history[channel] = deque(maxlen=1000)  # 保存最近1000个数据点
    
    def process_channel_data(self, channel: int, data: Dict):
        """
        处理通道数据
        
        Args:
            channel: 通道号
            data: 数据字典
        """
        try:
            # 更新最新数据
            self.channel_data[channel] = data
            
            # 添加到历史数据
            timestamp = data.get('timestamp', time.time())
            data_point = {
                'timestamp': timestamp,
                'pv': data.get('pv', 0),
                'sv': data.get('sv', 0),
                'mv': data.get('mv', 0),
                'status': data.get('status', 'UNKNOWN')
            }
            self.data_history[channel].append(data_point)
            
            # 检查报警
            alarms = self.alarm_manager.check_alarms(
                channel,
                data.get('pv', 0),
                data.get('sv', 0),
                data.get('mv', 0),
                data.get('status', 'UNKNOWN')
            )
            
            # 处理报警
            for alarm in alarms:
                self._handle_alarm(alarm)
            
            # 记录数据到数据库
            self._log_to_database(channel, data_point)
            
            # 记录数据到文件
            self._log_to_file(channel, data_point)
            
            # 发送信号
            self.data_updated.emit(channel, data)
            
            # 更新统计
            self.statistics['total_samples'] += 1
            self.statistics['last_update'] = datetime.now()
            
        except Exception as e:
            self.logger.error(f"处理通道{channel}数据失败: {e}")
    
    def _handle_alarm(self, alarm: Dict):
        """处理报警"""
        try:
            channel = alarm['channel']
            
            # 记录报警到数据库
            # 创建可序列化的详细信息
            serializable_details = {
                'channel': alarm['channel'],
                'rule_name': alarm['rule_name'],
                'name': alarm['name'],
                'level': alarm['level'],
                'message': alarm['message'],
                'timestamp': alarm['timestamp'].isoformat(),
                'pv': alarm['pv'],
                'sv': alarm['sv'],
                'mv': alarm['mv'],
                'status': alarm['status']
            }

            self.database_manager.log_system_event(
                event_type='ALARM',
                message=f"通道{channel}: {alarm['message']}",
                channel=channel,
                details=serializable_details
            )
            
            # 发送报警信号
            self.alarm_triggered.emit(channel, alarm)
            
            # 更新统计
            self.statistics['alarm_count'] += 1
            
            self.logger.warning(f"报警触发 - 通道{channel}: {alarm['message']}")
            
        except Exception as e:
            self.logger.error(f"处理报警失败: {e}")
    
    def _log_to_database(self, channel: int, data_point: Dict):
        """记录数据到数据库"""
        try:
            self.database_manager.log_data(
                channel=channel,
                timestamp=datetime.fromtimestamp(data_point['timestamp']),
                pv=data_point['pv'],
                sv=data_point['sv'],
                mv=data_point['mv'],
                status=data_point['status']
            )
        except Exception as e:
            self.logger.error(f"记录数据到数据库失败: {e}")
    
    def _log_to_file(self, channel: int, data_point: Dict):
        """记录数据到文件"""
        try:
            self.data_logger.log_channel_data(
                channel=channel,
                timestamp=datetime.fromtimestamp(data_point['timestamp']),
                pv=data_point['pv'],
                sv=data_point['sv'],
                mv=data_point['mv'],
                status=data_point['status']
            )
        except Exception as e:
            self.logger.error(f"记录数据到文件失败: {e}")
    
    def _update_statistics(self):
        """更新统计信息"""
        try:
            # 计算活跃通道数
            active_channels = 0
            current_time = time.time()
            
            for channel, data in self.channel_data.items():
                last_update = data.get('timestamp', 0)
                if current_time - last_update < 10:  # 10秒内有数据更新
                    active_channels += 1
            
            self.statistics['active_channels'] = active_channels
            
            # 发送统计信号
            self.statistics_updated.emit(self.statistics.copy())
            
        except Exception as e:
            self.logger.error(f"更新统计信息失败: {e}")
    
    def get_channel_data(self, channel: int) -> Optional[Dict]:
        """获取通道最新数据"""
        return self.channel_data.get(channel)
    
    def get_channel_history(self, channel: int, duration_minutes: int = 60) -> List[Dict]:
        """
        获取通道历史数据
        
        Args:
            channel: 通道号
            duration_minutes: 时间长度(分钟)
            
        Returns:
            List[Dict]: 历史数据列表
        """
        if channel not in self.data_history:
            return []
        
        cutoff_time = time.time() - (duration_minutes * 60)
        history = []
        
        for data_point in self.data_history[channel]:
            if data_point['timestamp'] >= cutoff_time:
                history.append(data_point)
        
        return history
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        return self.statistics.copy()
    
    def add_alarm_rule(self, channel: int, rule_name: str, condition: Callable,
                      level: str = 'WARNING', message: str = ''):
        """添加报警规则"""
        self.alarm_manager.add_alarm_rule(channel, rule_name, condition, level, message)
    
    def remove_alarm_rule(self, channel: int, rule_name: str):
        """移除报警规则"""
        self.alarm_manager.remove_alarm_rule(channel, rule_name)
    
    def clear_statistics(self):
        """清除统计信息"""
        self.statistics = {
            'total_samples': 0,
            'active_channels': 0,
            'alarm_count': 0,
            'last_update': None
        }
