#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口界面
8通道烧结炉控制软件的主界面
"""

import logging
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QGridLayout, QMenuBar, QToolBar, QStatusBar, 
                            QAction, QMessageBox, QSplitter, QTabWidget,
                            QLabel, QPushButton, QGroupBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSlot
from PyQt5.QtGui import QIcon, QFont

from .channel_widget import ChannelWidget
from .chart_widget import ChartWidget
from .program_widget import ProgramWidget
from .material_widget import MaterialWidget
from .config_dialog import ConfigDialog
from .about_dialog import AboutDialog
from ..communication.serial_manager import SerialManager
from ..database.database_manager import DatabaseManager
from ..utils.logger import DataLogger


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self, config_manager):
        """
        初始化主窗口
        
        Args:
            config_manager: 配置管理器实例
        """
        super().__init__()
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.serial_manager = SerialManager(config_manager)
        self.database_manager = DatabaseManager(config_manager.get("database.path"))
        self.data_logger = DataLogger()
        
        # 通道控件列表
        self.channel_widgets = []
        
        # 初始化界面
        self._init_ui()
        self._init_connections()
        self._load_settings()
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_status)
        self.status_timer.start(1000)  # 每秒更新一次状态
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("8通道烧结炉控制软件 v1.0")
        self.setMinimumSize(1200, 800)
        
        # 创建菜单栏
        self._create_menu_bar()
        
        # 创建工具栏
        self._create_tool_bar()
        
        # 创建状态栏
        self._create_status_bar()
        
        # 创建中央控件
        self._create_central_widget()
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        # 连接串口
        self.connect_action = QAction('连接串口', self)
        self.connect_action.setShortcut('Ctrl+C')
        self.connect_action.triggered.connect(self._toggle_connection)
        file_menu.addAction(self.connect_action)
        
        file_menu.addSeparator()
        
        # 导出数据
        export_action = QAction('导出数据...', self)
        export_action.setShortcut('Ctrl+E')
        export_action.triggered.connect(self._export_data)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction('退出', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 控制菜单
        control_menu = menubar.addMenu('控制(&C)')
        
        # 启动所有
        start_all_action = QAction('启动所有通道', self)
        start_all_action.triggered.connect(self._start_all_channels)
        control_menu.addAction(start_all_action)
        
        # 停止所有
        stop_all_action = QAction('停止所有通道', self)
        stop_all_action.triggered.connect(self._stop_all_channels)
        control_menu.addAction(stop_all_action)
        
        # 暂停所有
        hold_all_action = QAction('暂停所有通道', self)
        hold_all_action.triggered.connect(self._hold_all_channels)
        control_menu.addAction(hold_all_action)
        
        # 设置菜单
        settings_menu = menubar.addMenu('设置(&S)')
        
        # 串口配置
        serial_config_action = QAction('串口配置...', self)
        serial_config_action.triggered.connect(self._show_config_dialog)
        settings_menu.addAction(serial_config_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        # 关于
        about_action = QAction('关于...', self)
        about_action.triggered.connect(self._show_about_dialog)
        help_menu.addAction(about_action)
    
    def _create_tool_bar(self):
        """创建工具栏"""
        toolbar = self.addToolBar('主工具栏')
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # 连接按钮
        self.connect_btn = QPushButton('连接')
        self.connect_btn.clicked.connect(self._toggle_connection)
        toolbar.addWidget(self.connect_btn)
        
        toolbar.addSeparator()
        
        # 控制按钮
        start_btn = QPushButton('启动全部')
        start_btn.clicked.connect(self._start_all_channels)
        toolbar.addWidget(start_btn)
        
        stop_btn = QPushButton('停止全部')
        stop_btn.clicked.connect(self._stop_all_channels)
        toolbar.addWidget(stop_btn)
        
        hold_btn = QPushButton('暂停全部')
        hold_btn.clicked.connect(self._hold_all_channels)
        toolbar.addWidget(hold_btn)
        
        toolbar.addSeparator()
        
        # 导出按钮
        export_btn = QPushButton('导出数据')
        export_btn.clicked.connect(self._export_data)
        toolbar.addWidget(export_btn)
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()
        
        # 连接状态标签
        self.connection_label = QLabel('未连接')
        self.status_bar.addWidget(self.connection_label)
        
        # 数据状态标签
        self.data_label = QLabel('数据: 0 条')
        self.status_bar.addPermanentWidget(self.data_label)
        
        # 时间标签
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)
    
    def _create_central_widget(self):
        """创建中央控件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧：通道监控区域
        self._create_channels_area(splitter)
        
        # 右侧：功能选项卡
        self._create_function_tabs(splitter)
        
        # 设置分割器比例
        splitter.setSizes([800, 400])
    
    def _create_channels_area(self, parent):
        """创建通道监控区域"""
        channels_widget = QWidget()
        parent.addWidget(channels_widget)
        
        layout = QVBoxLayout(channels_widget)
        
        # 标题
        title_label = QLabel('8通道实时监控')
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 通道网格
        channels_grid = QGridLayout()
        layout.addLayout(channels_grid)
        
        # 创建8个通道控件
        for i in range(8):
            channel_widget = ChannelWidget(i + 1)
            self.channel_widgets.append(channel_widget)
            
            row = i // 4
            col = i % 4
            channels_grid.addWidget(channel_widget, row, col)
        
        # 添加弹性空间
        layout.addStretch()
    
    def _create_function_tabs(self, parent):
        """创建功能选项卡"""
        tab_widget = QTabWidget()
        parent.addWidget(tab_widget)
        
        # 实时曲线选项卡
        self.chart_widget = ChartWidget()
        tab_widget.addTab(self.chart_widget, '实时曲线')
        
        # 程序管理选项卡
        self.program_widget = ProgramWidget(self.database_manager)
        tab_widget.addTab(self.program_widget, '程序管理')
        
        # 材料库选项卡
        self.material_widget = MaterialWidget(self.database_manager)
        tab_widget.addTab(self.material_widget, '材料库')
    
    def _init_connections(self):
        """初始化信号连接"""
        # 串口管理器信号
        self.serial_manager.data_received.connect(self._on_data_received)
        self.serial_manager.connection_status_changed.connect(self._on_connection_status_changed)
        self.serial_manager.error_occurred.connect(self._on_error_occurred)
    
    def _load_settings(self):
        """加载设置"""
        # 加载窗口大小和位置
        window_size = self.config_manager.get("ui.window_size", [1200, 800])
        window_pos = self.config_manager.get("ui.window_position", [100, 100])
        
        self.resize(window_size[0], window_size[1])
        self.move(window_pos[0], window_pos[1])
    
    def _save_settings(self):
        """保存设置"""
        # 保存窗口大小和位置
        self.config_manager.set("ui.window_size", [self.width(), self.height()])
        self.config_manager.set("ui.window_position", [self.x(), self.y()])
        self.config_manager.save_config()
    
    @pyqtSlot(int, dict)
    def _on_data_received(self, channel, data):
        """处理接收到的数据"""
        # 更新通道控件显示
        if 1 <= channel <= 8:
            widget_index = channel - 1
            self.channel_widgets[widget_index].update_data(data)
        
        # 更新实时曲线
        self.chart_widget.add_data_point(channel, data)
        
        # 记录数据到数据库
        from datetime import datetime
        self.database_manager.log_data(
            channel=channel,
            timestamp=datetime.fromtimestamp(data.get('timestamp', 0)),
            pv=data.get('pv', 0),
            sv=data.get('sv', 0),
            mv=data.get('mv', 0),
            status=data.get('status', 'UNKNOWN')
        )
        
        # 记录数据到文件
        self.data_logger.log_channel_data(
            channel=channel,
            timestamp=datetime.fromtimestamp(data.get('timestamp', 0)),
            pv=data.get('pv', 0),
            sv=data.get('sv', 0),
            mv=data.get('mv', 0),
            status=data.get('status', 'UNKNOWN')
        )

    @pyqtSlot(bool)
    def _on_connection_status_changed(self, connected):
        """处理连接状态变化"""
        if connected:
            self.connection_label.setText('已连接')
            self.connect_btn.setText('断开')
            self.connect_action.setText('断开串口')

            # 启动数据轮询
            self.serial_manager.start_polling()

            # 记录系统事件
            self.database_manager.log_system_event('CONNECTION', '串口连接成功')

        else:
            self.connection_label.setText('未连接')
            self.connect_btn.setText('连接')
            self.connect_action.setText('连接串口')

            # 停止数据轮询
            self.serial_manager.stop_polling()

            # 记录系统事件
            self.database_manager.log_system_event('CONNECTION', '串口连接断开')

    @pyqtSlot(str)
    def _on_error_occurred(self, error_message):
        """处理错误"""
        self.logger.error(f"通讯错误: {error_message}")
        QMessageBox.warning(self, '通讯错误', error_message)

        # 记录系统事件
        self.database_manager.log_system_event('ERROR', error_message)

    def _toggle_connection(self):
        """切换连接状态"""
        if self.serial_manager.is_connected:
            self.serial_manager.disconnect()
        else:
            if not self.serial_manager.connect():
                QMessageBox.critical(self, '连接失败', '无法连接到串口，请检查配置')

    def _start_all_channels(self):
        """启动所有通道"""
        for i in range(1, 9):
            self.serial_manager.send_control_command(i, 'START')

        self.database_manager.log_system_event('CONTROL', '启动所有通道')
        self.logger.info("启动所有通道")

    def _stop_all_channels(self):
        """停止所有通道"""
        for i in range(1, 9):
            self.serial_manager.send_control_command(i, 'STOP')

        self.database_manager.log_system_event('CONTROL', '停止所有通道')
        self.logger.info("停止所有通道")

    def _hold_all_channels(self):
        """暂停所有通道"""
        for i in range(1, 9):
            self.serial_manager.send_control_command(i, 'HOLD')

        self.database_manager.log_system_event('CONTROL', '暂停所有通道')
        self.logger.info("暂停所有通道")

    def _export_data(self):
        """导出数据"""
        from .export_dialog import ExportDialog
        dialog = ExportDialog(self.database_manager, self)
        dialog.exec_()

    def _show_config_dialog(self):
        """显示配置对话框"""
        dialog = ConfigDialog(self.config_manager, self)
        if dialog.exec_() == ConfigDialog.Accepted:
            # 重新加载配置
            self.serial_manager._load_config()

    def _show_about_dialog(self):
        """显示关于对话框"""
        dialog = AboutDialog(self)
        dialog.exec_()

    def _update_status(self):
        """更新状态栏"""
        from datetime import datetime

        # 更新时间
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.time_label.setText(current_time)

        # 更新数据统计（这里可以添加更多状态信息）
        # data_count = self.database_manager.get_data_count()
        # self.data_label.setText(f'数据: {data_count} 条')

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 保存设置
        self._save_settings()

        # 断开连接
        if self.serial_manager.is_connected:
            self.serial_manager.disconnect()

        # 记录系统事件
        self.database_manager.log_system_event('SYSTEM', '应用程序关闭')

        event.accept()
