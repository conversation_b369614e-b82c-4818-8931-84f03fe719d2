#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理器
负责SQLite数据库的创建、管理和操作
"""

import sqlite3
import os
import logging
import json
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from contextlib import contextmanager


class DatabaseManager:
    """数据库管理器类"""
    
    def __init__(self, db_path: str = "data/furnace_control.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # 确保数据库目录存在
        db_dir = os.path.dirname(db_path)
        if db_dir and db_dir.strip() and not os.path.exists(db_dir):
            os.makedirs(db_dir)
        
        # 初始化数据库
        self._init_database()
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 创建材料库表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS materials (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL UNIQUE,
                        melting_point REAL,
                        sintering_temp REAL,
                        description TEXT,
                        properties TEXT,  -- JSON格式存储其他属性
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建程序库表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS programs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL UNIQUE,
                        description TEXT,
                        material_id INTEGER,
                        segments TEXT NOT NULL,  -- JSON格式存储程序段
                        total_time INTEGER,      -- 总时间(分钟)
                        max_temp REAL,          -- 最高温度
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (material_id) REFERENCES materials (id)
                    )
                ''')
                
                # 创建数据日志表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS data_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        channel INTEGER NOT NULL,
                        timestamp TIMESTAMP NOT NULL,
                        pv REAL NOT NULL,        -- 当前值
                        sv REAL NOT NULL,        -- 设定值
                        mv REAL NOT NULL,        -- 输出值
                        status TEXT NOT NULL,    -- 运行状态
                        alarm TEXT,              -- 报警信息
                        program_id INTEGER,      -- 关联的程序ID
                        FOREIGN KEY (program_id) REFERENCES programs (id)
                    )
                ''')
                
                # 创建系统事件日志表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_events (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        event_type TEXT NOT NULL,
                        channel INTEGER,
                        message TEXT NOT NULL,
                        details TEXT             -- JSON格式存储详细信息
                    )
                ''')
                
                # 创建运行记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS run_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        program_id INTEGER NOT NULL,
                        channels TEXT NOT NULL,  -- JSON格式存储使用的通道
                        start_time TIMESTAMP NOT NULL,
                        end_time TIMESTAMP,
                        status TEXT NOT NULL,    -- RUNNING, COMPLETED, STOPPED, ERROR
                        notes TEXT,
                        FOREIGN KEY (program_id) REFERENCES programs (id)
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_data_logs_channel_time ON data_logs (channel, timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_system_events_time ON system_events (timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_run_records_time ON run_records (start_time)')
                
                conn.commit()
                self.logger.info("数据库初始化完成")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    # 材料库管理
    def add_material(self, name: str, melting_point: float = None, 
                    sintering_temp: float = None, description: str = None,
                    properties: Dict[str, Any] = None) -> int:
        """
        添加材料
        
        Args:
            name: 材料名称
            melting_point: 熔点
            sintering_temp: 烧结温度
            description: 描述
            properties: 其他属性
            
        Returns:
            int: 材料ID
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                properties_json = json.dumps(properties) if properties else None
                
                cursor.execute('''
                    INSERT INTO materials (name, melting_point, sintering_temp, description, properties)
                    VALUES (?, ?, ?, ?, ?)
                ''', (name, melting_point, sintering_temp, description, properties_json))
                
                material_id = cursor.lastrowid
                conn.commit()
                
                self.logger.info(f"添加材料成功: {name} (ID: {material_id})")
                return material_id
                
        except sqlite3.IntegrityError:
            self.logger.error(f"材料名称已存在: {name}")
            raise ValueError(f"材料名称已存在: {name}")
        except Exception as e:
            self.logger.error(f"添加材料失败: {e}")
            raise
    
    def get_materials(self) -> List[Dict[str, Any]]:
        """
        获取所有材料
        
        Returns:
            List[Dict[str, Any]]: 材料列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM materials ORDER BY name')
                
                materials = []
                for row in cursor.fetchall():
                    material = dict(row)
                    if material['properties']:
                        material['properties'] = json.loads(material['properties'])
                    materials.append(material)
                
                return materials
                
        except Exception as e:
            self.logger.error(f"获取材料列表失败: {e}")
            return []
    
    def update_material(self, material_id: int, **kwargs) -> bool:
        """
        更新材料信息
        
        Args:
            material_id: 材料ID
            **kwargs: 要更新的字段
            
        Returns:
            bool: 更新成功返回True
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 构建更新语句
                set_clauses = []
                values = []
                
                for key, value in kwargs.items():
                    if key in ['name', 'melting_point', 'sintering_temp', 'description']:
                        set_clauses.append(f"{key} = ?")
                        values.append(value)
                    elif key == 'properties':
                        set_clauses.append("properties = ?")
                        values.append(json.dumps(value) if value else None)
                
                if not set_clauses:
                    return False
                
                set_clauses.append("updated_at = CURRENT_TIMESTAMP")
                values.append(material_id)
                
                sql = f"UPDATE materials SET {', '.join(set_clauses)} WHERE id = ?"
                cursor.execute(sql, values)
                
                conn.commit()
                return cursor.rowcount > 0
                
        except Exception as e:
            self.logger.error(f"更新材料失败: {e}")
            return False
    
    def delete_material(self, material_id: int) -> bool:
        """
        删除材料
        
        Args:
            material_id: 材料ID
            
        Returns:
            bool: 删除成功返回True
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM materials WHERE id = ?', (material_id,))
                conn.commit()
                
                return cursor.rowcount > 0
                
        except Exception as e:
            self.logger.error(f"删除材料失败: {e}")
            return False

    # 程序库管理
    def add_program(self, name: str, segments: List[Dict[str, Any]],
                   description: str = None, material_id: int = None) -> int:
        """
        添加控温程序

        Args:
            name: 程序名称
            segments: 程序段列表 [{"temp": 100, "time": 60, "end_flag": 0}, ...]
            description: 程序描述
            material_id: 关联材料ID

        Returns:
            int: 程序ID
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 计算总时间和最高温度
                total_time = sum(seg.get('time', 0) for seg in segments)
                max_temp = max(seg.get('temp', 0) for seg in segments) if segments else 0

                segments_json = json.dumps(segments)

                cursor.execute('''
                    INSERT INTO programs (name, description, material_id, segments, total_time, max_temp)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (name, description, material_id, segments_json, total_time, max_temp))

                program_id = cursor.lastrowid
                conn.commit()

                self.logger.info(f"添加程序成功: {name} (ID: {program_id})")
                return program_id

        except sqlite3.IntegrityError:
            self.logger.error(f"程序名称已存在: {name}")
            raise ValueError(f"程序名称已存在: {name}")
        except Exception as e:
            self.logger.error(f"添加程序失败: {e}")
            raise

    def get_programs(self) -> List[Dict[str, Any]]:
        """
        获取所有程序

        Returns:
            List[Dict[str, Any]]: 程序列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT p.*, m.name as material_name
                    FROM programs p
                    LEFT JOIN materials m ON p.material_id = m.id
                    ORDER BY p.name
                ''')

                programs = []
                for row in cursor.fetchall():
                    program = dict(row)
                    program['segments'] = json.loads(program['segments'])
                    programs.append(program)

                return programs

        except Exception as e:
            self.logger.error(f"获取程序列表失败: {e}")
            return []

    def get_program(self, program_id: int) -> Optional[Dict[str, Any]]:
        """
        获取单个程序

        Args:
            program_id: 程序ID

        Returns:
            Dict[str, Any]: 程序信息
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT p.*, m.name as material_name
                    FROM programs p
                    LEFT JOIN materials m ON p.material_id = m.id
                    WHERE p.id = ?
                ''', (program_id,))

                row = cursor.fetchone()
                if row:
                    program = dict(row)
                    program['segments'] = json.loads(program['segments'])
                    return program

                return None

        except Exception as e:
            self.logger.error(f"获取程序失败: {e}")
            return None

    def update_program(self, program_id: int, **kwargs) -> bool:
        """
        更新程序信息

        Args:
            program_id: 程序ID
            **kwargs: 要更新的字段

        Returns:
            bool: 更新成功返回True
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 构建更新语句
                set_clauses = []
                values = []

                for key, value in kwargs.items():
                    if key in ['name', 'description', 'material_id']:
                        set_clauses.append(f"{key} = ?")
                        values.append(value)
                    elif key == 'segments':
                        set_clauses.append("segments = ?")
                        values.append(json.dumps(value))
                        # 重新计算总时间和最高温度
                        total_time = sum(seg.get('time', 0) for seg in value)
                        max_temp = max(seg.get('temp', 0) for seg in value) if value else 0
                        set_clauses.extend(["total_time = ?", "max_temp = ?"])
                        values.extend([total_time, max_temp])

                if not set_clauses:
                    return False

                set_clauses.append("updated_at = CURRENT_TIMESTAMP")
                values.append(program_id)

                sql = f"UPDATE programs SET {', '.join(set_clauses)} WHERE id = ?"
                cursor.execute(sql, values)

                conn.commit()
                return cursor.rowcount > 0

        except Exception as e:
            self.logger.error(f"更新程序失败: {e}")
            return False

    def delete_program(self, program_id: int) -> bool:
        """
        删除程序

        Args:
            program_id: 程序ID

        Returns:
            bool: 删除成功返回True
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM programs WHERE id = ?', (program_id,))
                conn.commit()

                return cursor.rowcount > 0

        except Exception as e:
            self.logger.error(f"删除程序失败: {e}")
            return False

    # 数据日志管理
    def log_data(self, channel: int, timestamp: datetime, pv: float, sv: float,
                mv: float, status: str, alarm: str = None, program_id: int = None) -> bool:
        """
        记录数据日志

        Args:
            channel: 通道号
            timestamp: 时间戳
            pv: 当前值
            sv: 设定值
            mv: 输出值
            status: 状态
            alarm: 报警信息
            program_id: 程序ID

        Returns:
            bool: 记录成功返回True
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO data_logs (channel, timestamp, pv, sv, mv, status, alarm, program_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (channel, timestamp, pv, sv, mv, status, alarm, program_id))

                conn.commit()
                return True

        except Exception as e:
            self.logger.error(f"记录数据日志失败: {e}")
            return False

    def get_data_logs(self, channel: int = None, start_time: datetime = None,
                     end_time: datetime = None, limit: int = 1000) -> List[Dict[str, Any]]:
        """
        获取数据日志

        Args:
            channel: 通道号
            start_time: 开始时间
            end_time: 结束时间
            limit: 限制条数

        Returns:
            List[Dict[str, Any]]: 数据日志列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 构建查询条件
                conditions = []
                params = []

                if channel is not None:
                    conditions.append("channel = ?")
                    params.append(channel)

                if start_time:
                    conditions.append("timestamp >= ?")
                    params.append(start_time)

                if end_time:
                    conditions.append("timestamp <= ?")
                    params.append(end_time)

                where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

                sql = f'''
                    SELECT * FROM data_logs
                    {where_clause}
                    ORDER BY timestamp DESC
                    LIMIT ?
                '''
                params.append(limit)

                cursor.execute(sql, params)

                logs = []
                for row in cursor.fetchall():
                    logs.append(dict(row))

                return logs

        except Exception as e:
            self.logger.error(f"获取数据日志失败: {e}")
            return []

    def log_system_event(self, event_type: str, message: str, channel: int = None,
                        details: Dict[str, Any] = None) -> bool:
        """
        记录系统事件

        Args:
            event_type: 事件类型
            message: 事件消息
            channel: 相关通道
            details: 详细信息

        Returns:
            bool: 记录成功返回True
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                details_json = json.dumps(details) if details else None

                cursor.execute('''
                    INSERT INTO system_events (event_type, channel, message, details)
                    VALUES (?, ?, ?, ?)
                ''', (event_type, channel, message, details_json))

                conn.commit()
                return True

        except Exception as e:
            self.logger.error(f"记录系统事件失败: {e}")
            return False

    def get_system_events(self, event_type: str = None, start_time: datetime = None,
                         end_time: datetime = None, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取系统事件

        Args:
            event_type: 事件类型
            start_time: 开始时间
            end_time: 结束时间
            limit: 限制条数

        Returns:
            List[Dict[str, Any]]: 系统事件列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 构建查询条件
                conditions = []
                params = []

                if event_type:
                    conditions.append("event_type = ?")
                    params.append(event_type)

                if start_time:
                    conditions.append("timestamp >= ?")
                    params.append(start_time)

                if end_time:
                    conditions.append("timestamp <= ?")
                    params.append(end_time)

                where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

                sql = f'''
                    SELECT * FROM system_events
                    {where_clause}
                    ORDER BY timestamp DESC
                    LIMIT ?
                '''
                params.append(limit)

                cursor.execute(sql, params)

                events = []
                for row in cursor.fetchall():
                    event = dict(row)
                    if event['details']:
                        event['details'] = json.loads(event['details'])
                    events.append(event)

                return events

        except Exception as e:
            self.logger.error(f"获取系统事件失败: {e}")
            return []
