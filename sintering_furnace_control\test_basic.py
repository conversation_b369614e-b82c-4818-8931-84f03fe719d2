#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本功能测试脚本
测试核心模块的基本功能
"""

import sys
import os
import time
import random
from datetime import datetime

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.config_manager import ConfigManager
from src.database.database_manager import DatabaseManager
from src.communication.aibus_protocol import AIBUSProtocol, ProgramSegment
from src.core.monitoring_manager import MonitoringManager


def test_config_manager():
    """测试配置管理器"""
    print("=== 测试配置管理器 ===")
    
    config = ConfigManager("test_config.json")
    
    # 测试获取配置
    serial_config = config.get_serial_config()
    print(f"串口配置: {serial_config}")
    
    # 测试设置配置
    config.set("test.value", "hello world")
    test_value = config.get("test.value")
    print(f"测试值: {test_value}")
    
    # 测试保存配置
    config.save_config()
    print("配置管理器测试完成")


def test_database_manager():
    """测试数据库管理器"""
    print("\n=== 测试数据库管理器 ===")
    
    db = DatabaseManager("test_database.db")
    
    # 测试添加材料
    try:
        material_id = db.add_material(
            name="测试材料",
            melting_point=1000.0,
            sintering_temp=800.0,
            description="这是一个测试材料"
        )
        print(f"添加材料成功，ID: {material_id}")
    except Exception as e:
        print(f"添加材料失败: {e}")
    
    # 测试获取材料列表
    materials = db.get_materials()
    print(f"材料列表: {len(materials)} 个材料")
    
    # 测试添加程序
    try:
        segments = [
            {"temp": 100, "time": 30, "end_flag": 0},
            {"temp": 200, "time": 60, "end_flag": 0},
            {"temp": 300, "time": 90, "end_flag": -121}
        ]
        program_id = db.add_program(
            name="测试程序",
            segments=segments,
            description="这是一个测试程序"
        )
        print(f"添加程序成功，ID: {program_id}")
    except Exception as e:
        print(f"添加程序失败: {e}")
    
    # 测试获取程序列表
    programs = db.get_programs()
    print(f"程序列表: {len(programs)} 个程序")
    
    # 测试记录数据
    db.log_data(
        channel=1,
        timestamp=datetime.now(),
        pv=25.5,
        sv=30.0,
        mv=50,
        status="RUN"
    )
    print("数据记录测试完成")
    
    print("数据库管理器测试完成")


def test_aibus_protocol():
    """测试AIBUS协议"""
    print("\n=== 测试AIBUS协议 ===")
    
    protocol = AIBUSProtocol()
    
    # 测试创建读取指令
    read_cmd = protocol.create_read_command(1, 0x00)
    print(f"读取指令: {read_cmd.hex()}")
    
    # 测试创建写入指令
    write_cmd = protocol.create_write_command(1, 0x00, 250)  # 设定值25.0°C
    print(f"写入指令: {write_cmd.hex()}")
    
    # 测试程序段
    segment = ProgramSegment(target_temp=100.0, time_minutes=30, end_flag=0)
    segment_bytes = segment.to_bytes()
    print(f"程序段数据: {segment_bytes.hex()}")
    
    # 测试从字节创建程序段
    restored_segment = ProgramSegment.from_bytes(segment_bytes)
    print(f"恢复的程序段: {restored_segment}")
    
    print("AIBUS协议测试完成")


def test_monitoring_manager():
    """测试监控管理器"""
    print("\n=== 测试监控管理器 ===")
    
    config = ConfigManager("test_config.json")
    db = DatabaseManager("test_database.db")
    
    # 创建监控管理器
    monitor = MonitoringManager(config, db)
    
    # 模拟数据处理
    for i in range(5):
        for channel in range(1, 4):  # 测试前3个通道
            # 生成模拟数据
            data = {
                'pv': random.uniform(20, 100),
                'sv': random.uniform(25, 105),
                'mv': random.randint(0, 100),
                'status': random.choice(['RUN', 'STOP', 'HOLD']),
                'timestamp': time.time()
            }
            
            # 处理数据
            monitor.process_channel_data(channel, data)
        
        time.sleep(0.1)  # 短暂延时
    
    # 获取统计信息
    stats = monitor.get_statistics()
    print(f"统计信息: {stats}")
    
    # 获取通道历史数据
    history = monitor.get_channel_history(1, duration_minutes=1)
    print(f"通道1历史数据: {len(history)} 个数据点")
    
    print("监控管理器测试完成")


def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        "test_config.json",
        "test_database.db"
    ]
    
    for file in test_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"删除测试文件: {file}")
            except Exception as e:
                print(f"删除文件失败 {file}: {e}")


def main():
    """主测试函数"""
    print("开始基本功能测试...")
    
    try:
        test_config_manager()
        test_database_manager()
        test_aibus_protocol()
        test_monitoring_manager()
        
        print("\n=== 所有测试完成 ===")
        print("基本功能测试通过！")
        
    except Exception as e:
        print(f"\n测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        cleanup_test_files()


if __name__ == "__main__":
    main()
