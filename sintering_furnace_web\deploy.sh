#!/bin/bash

# 8通道烧结炉控制系统 - Web版本部署脚本

set -e

echo "========================================"
echo "8通道烧结炉控制系统 - Web版本部署"
echo "========================================"

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误: Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 创建必要的目录
echo "创建目录结构..."
mkdir -p data logs config deploy/ssl

# 生成自签名SSL证书（仅用于测试）
if [ ! -f "deploy/ssl/cert.pem" ]; then
    echo "生成SSL证书..."
    openssl req -x509 -newkey rsa:4096 -keyout deploy/ssl/key.pem -out deploy/ssl/cert.pem -days 365 -nodes \
        -subj "/C=CN/ST=State/L=City/O=Organization/CN=localhost"
    echo "SSL证书已生成（仅用于测试）"
fi

# 检查配置文件
if [ ! -f "config/web_config.json" ]; then
    echo "警告: 配置文件不存在，将使用默认配置"
fi

# 构建和启动服务
echo "构建Docker镜像..."
docker-compose -f deploy/docker-compose.yml build

echo "启动服务..."
docker-compose -f deploy/docker-compose.yml up -d

# 等待服务启动
echo "等待服务启动..."
sleep 10

# 检查服务状态
echo "检查服务状态..."
docker-compose -f deploy/docker-compose.yml ps

# 测试服务
echo "测试服务连接..."
if curl -k -s https://localhost/health > /dev/null; then
    echo "✓ 服务启动成功"
    echo "✓ Web界面: https://localhost"
    echo "✓ API文档: https://localhost/docs"
else
    echo "✗ 服务启动失败，请检查日志"
    docker-compose -f deploy/docker-compose.yml logs
    exit 1
fi

echo "========================================"
echo "部署完成！"
echo "========================================"
echo "访问地址:"
echo "  Web界面: https://localhost"
echo "  API文档: https://localhost/docs"
echo ""
echo "管理命令:"
echo "  查看日志: docker-compose -f deploy/docker-compose.yml logs -f"
echo "  停止服务: docker-compose -f deploy/docker-compose.yml down"
echo "  重启服务: docker-compose -f deploy/docker-compose.yml restart"
echo "========================================"
