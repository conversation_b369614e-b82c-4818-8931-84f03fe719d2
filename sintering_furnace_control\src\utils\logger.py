#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志系统工具
提供统一的日志记录功能
"""

import os
import logging
import logging.handlers
from datetime import datetime
from typing import Optional


def setup_logger(
    name: str = "SinteringFurnaceControl",
    log_level: str = "INFO",
    log_dir: str = "logs",
    max_log_files: int = 30
) -> logging.Logger:
    """
    设置日志系统
    
    Args:
        name: 日志器名称
        log_level: 日志级别
        log_dir: 日志目录
        max_log_files: 最大日志文件数
        
    Returns:
        配置好的日志器
    """
    # 确保日志目录存在
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 创建日志器
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # 如果已经有处理器，先清除
    if logger.handlers:
        logger.handlers.clear()
    
    # 创建格式器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器 - 按日期轮转
    log_file = os.path.join(log_dir, f"{name}_{datetime.now().strftime('%Y%m%d')}.log")
    file_handler = logging.handlers.TimedRotatingFileHandler(
        log_file,
        when='midnight',
        interval=1,
        backupCount=max_log_files,
        encoding='utf-8'
    )
    file_handler.setLevel(getattr(logging, log_level.upper()))
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # 错误日志文件处理器
    error_log_file = os.path.join(log_dir, f"{name}_error_{datetime.now().strftime('%Y%m%d')}.log")
    error_handler = logging.handlers.TimedRotatingFileHandler(
        error_log_file,
        when='midnight',
        interval=1,
        backupCount=max_log_files,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)
    logger.addHandler(error_handler)
    
    return logger


class DataLogger:
    """数据日志记录器"""
    
    def __init__(self, log_dir: str = "log_data"):
        """
        初始化数据日志记录器
        
        Args:
            log_dir: 数据日志目录
        """
        self.log_dir = log_dir
        self.logger = logging.getLogger(__name__)
        
        # 确保日志目录存在
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
    
    def log_channel_data(
        self,
        channel: int,
        timestamp: datetime,
        pv: float,
        sv: float,
        mv: float,
        status: str,
        alarm: Optional[str] = None
    ):
        """
        记录通道数据
        
        Args:
            channel: 通道号
            timestamp: 时间戳
            pv: 当前值
            sv: 设定值
            mv: 输出值
            status: 状态
            alarm: 报警信息
        """
        try:
            # 按日期和通道创建日志文件
            date_str = timestamp.strftime('%Y%m%d')
            log_file = os.path.join(self.log_dir, f"channel_{channel:02d}_{date_str}.csv")
            
            # 检查文件是否存在，不存在则创建并写入表头
            file_exists = os.path.exists(log_file)
            
            with open(log_file, 'a', encoding='utf-8') as f:
                if not file_exists:
                    # 写入CSV表头
                    f.write("Timestamp,Channel,PV,SV,MV,Status,Alarm\n")
                
                # 写入数据
                alarm_str = alarm if alarm else ""
                f.write(f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')},{channel},{pv},{sv},{mv},{status},{alarm_str}\n")
                
        except Exception as e:
            self.logger.error(f"记录通道{channel}数据失败: {e}")
    
    def log_system_event(self, event_type: str, message: str, channel: Optional[int] = None):
        """
        记录系统事件
        
        Args:
            event_type: 事件类型
            message: 事件消息
            channel: 相关通道号
        """
        try:
            timestamp = datetime.now()
            date_str = timestamp.strftime('%Y%m%d')
            log_file = os.path.join(self.log_dir, f"system_events_{date_str}.csv")
            
            # 检查文件是否存在，不存在则创建并写入表头
            file_exists = os.path.exists(log_file)
            
            with open(log_file, 'a', encoding='utf-8') as f:
                if not file_exists:
                    # 写入CSV表头
                    f.write("Timestamp,EventType,Channel,Message\n")
                
                # 写入数据
                channel_str = str(channel) if channel is not None else ""
                f.write(f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')},{event_type},{channel_str},{message}\n")
                
        except Exception as e:
            self.logger.error(f"记录系统事件失败: {e}")


def get_logger(name: str) -> logging.Logger:
    """
    获取日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        日志器实例
    """
    return logging.getLogger(name)
