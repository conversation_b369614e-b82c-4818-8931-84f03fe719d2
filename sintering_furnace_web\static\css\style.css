/* 8通道烧结炉控制系统样式文件 */

/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
}

#connection-status {
    color: #28a745;
}

#connection-status.disconnected {
    color: #dc3545;
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 通道卡片样式 */
.channel-card {
    border: 2px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.channel-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.channel-card.running {
    border-color: #28a745;
    background-color: #f8fff9;
}

.channel-card.stopped {
    border-color: #dc3545;
    background-color: #fff8f8;
}

.channel-card.paused {
    border-color: #ffc107;
    background-color: #fffdf5;
}

.channel-card.error {
    border-color: #dc3545;
    background-color: #fff5f5;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* 通道标题 */
.channel-title {
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 10px;
}

/* 数据显示 */
.data-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.data-label {
    font-weight: 500;
    color: #6c757d;
}

.data-value {
    font-weight: bold;
    font-size: 1.1rem;
}

.pv-value {
    color: #007bff;
}

.sv-value {
    color: #28a745;
}

.mv-value {
    color: #ffc107;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-running {
    background-color: #28a745;
}

.status-stopped {
    background-color: #dc3545;
}

.status-paused {
    background-color: #ffc107;
}

.status-error {
    background-color: #dc3545;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* 控制按钮 */
.control-buttons {
    margin-top: 10px;
}

.control-buttons .btn {
    margin-right: 5px;
    margin-bottom: 5px;
}

/* 报警样式 */
.alarm-badge {
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    animation: pulse 2s infinite;
}

/* 程序段样式 */
.program-segment {
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 10px;
    background-color: #fff;
}

.program-segment-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 10px;
}

.segment-number {
    font-weight: bold;
    color: #007bff;
}

.remove-segment-btn {
    color: #dc3545;
    cursor: pointer;
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 400px;
    margin: 20px 0;
}

/* 程序列表 */
.program-item {
    cursor: pointer;
    transition: background-color 0.2s;
}

.program-item:hover {
    background-color: #f8f9fa;
}

.program-item.active {
    background-color: #e3f2fd;
    border-color: #2196f3;
}

/* 配置表单 */
.config-form {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 通道配置项 */
.channel-config-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.channel-config-item:last-child {
    border-bottom: none;
}

.channel-config-label {
    flex: 1;
    font-weight: 500;
}

.channel-config-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .channel-card {
        margin-bottom: 10px;
    }
    
    .data-row {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .control-buttons .btn {
        width: 100%;
        margin-bottom: 5px;
    }
    
    .navbar-nav {
        text-align: center;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 10px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .btn-group .btn {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip-inner {
    background-color: #333;
    color: #fff;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 12px;
}

/* 成功/错误消息 */
.alert-success {
    border-color: #28a745;
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    border-color: #dc3545;
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    border-color: #ffc107;
    background-color: #fff3cd;
    color: #856404;
}

/* 数据表格 */
.data-table {
    font-size: 0.9rem;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.data-table td {
    vertical-align: middle;
}

/* 自定义滚动条 */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #007bff #f1f1f1;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #007bff;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}
