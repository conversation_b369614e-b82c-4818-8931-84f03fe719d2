#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
程序管理控件
用于管理控温程序的界面组件
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QGroupBox, QMessageBox)
from PyQt5.QtCore import Qt


class ProgramWidget(QWidget):
    """程序管理控件类"""
    
    def __init__(self, database_manager):
        """
        初始化程序管理控件
        
        Args:
            database_manager: 数据库管理器实例
        """
        super().__init__()
        self.database_manager = database_manager
        self._init_ui()
        self._load_programs()
    
    def _init_ui(self):
        """初始化用户界面"""
        main_layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("控温程序管理")
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; margin: 10px;")
        main_layout.addWidget(title_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        main_layout.addLayout(button_layout)
        
        new_btn = QPushButton("新建程序")
        new_btn.clicked.connect(self._new_program)
        button_layout.addWidget(new_btn)
        
        edit_btn = QPushButton("编辑程序")
        edit_btn.clicked.connect(self._edit_program)
        button_layout.addWidget(edit_btn)
        
        delete_btn = QPushButton("删除程序")
        delete_btn.clicked.connect(self._delete_program)
        button_layout.addWidget(delete_btn)
        
        button_layout.addStretch()
        
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self._load_programs)
        button_layout.addWidget(refresh_btn)
        
        # 程序列表表格
        self.program_table = QTableWidget()
        self.program_table.setColumnCount(5)
        self.program_table.setHorizontalHeaderLabels(["ID", "程序名称", "描述", "总时间(分)", "最高温度(°C)"])
        self.program_table.setSelectionBehavior(QTableWidget.SelectRows)
        main_layout.addWidget(self.program_table)
    
    def _load_programs(self):
        """加载程序列表"""
        programs = self.database_manager.get_programs()
        
        self.program_table.setRowCount(len(programs))
        
        for row, program in enumerate(programs):
            self.program_table.setItem(row, 0, QTableWidgetItem(str(program['id'])))
            self.program_table.setItem(row, 1, QTableWidgetItem(program['name']))
            self.program_table.setItem(row, 2, QTableWidgetItem(program.get('description', '')))
            self.program_table.setItem(row, 3, QTableWidgetItem(str(program.get('total_time', 0))))
            self.program_table.setItem(row, 4, QTableWidgetItem(str(program.get('max_temp', 0))))
        
        self.program_table.resizeColumnsToContents()
    
    def _new_program(self):
        """新建程序"""
        QMessageBox.information(self, "提示", "新建程序功能待实现")
    
    def _edit_program(self):
        """编辑程序"""
        QMessageBox.information(self, "提示", "编辑程序功能待实现")
    
    def _delete_program(self):
        """删除程序"""
        current_row = self.program_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要删除的程序")
            return
        
        program_id = int(self.program_table.item(current_row, 0).text())
        program_name = self.program_table.item(current_row, 1).text()
        
        reply = QMessageBox.question(self, "确认删除", 
                                   f"确定要删除程序 '{program_name}' 吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            if self.database_manager.delete_program(program_id):
                QMessageBox.information(self, "成功", "程序删除成功")
                self._load_programs()
            else:
                QMessageBox.critical(self, "错误", "程序删除失败")
