#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
程序管理器
负责控温程序的管理、读取、写入和执行
"""

import logging
from typing import List, Dict, Any, Optional
from PyQt5.QtCore import QObject, pyqtSignal

from ..communication.aibus_protocol import ProgramSegment


class ProgramManager(QObject):
    """程序管理器类"""
    
    # 信号定义
    program_loaded = pyqtSignal(int, dict)  # 通道号, 程序信息
    program_written = pyqtSignal(int, bool)  # 通道号, 写入成功
    program_started = pyqtSignal(int)  # 通道号
    program_stopped = pyqtSignal(int)  # 通道号
    program_completed = pyqtSignal(int)  # 通道号
    
    def __init__(self, database_manager, serial_manager):
        """
        初始化程序管理器
        
        Args:
            database_manager: 数据库管理器
            serial_manager: 串口管理器
        """
        super().__init__()
        self.database_manager = database_manager
        self.serial_manager = serial_manager
        self.logger = logging.getLogger(__name__)
        
        # 当前运行的程序
        self.running_programs = {}  # {channel: program_info}
        
        # 程序执行状态
        self.program_status = {}  # {channel: status_info}
    
    def load_program_from_database(self, program_id: int) -> Optional[Dict[str, Any]]:
        """
        从数据库加载程序
        
        Args:
            program_id: 程序ID
            
        Returns:
            Dict[str, Any]: 程序信息
        """
        try:
            program = self.database_manager.get_program(program_id)
            if program:
                self.logger.info(f"从数据库加载程序: {program['name']} (ID: {program_id})")
                return program
            else:
                self.logger.warning(f"程序不存在: ID {program_id}")
                return None
                
        except Exception as e:
            self.logger.error(f"加载程序失败: {e}")
            return None
    
    def create_program_segments(self, segments_data: List[Dict[str, Any]]) -> List[ProgramSegment]:
        """
        创建程序段列表
        
        Args:
            segments_data: 程序段数据列表
            
        Returns:
            List[ProgramSegment]: 程序段对象列表
        """
        segments = []
        
        for i, seg_data in enumerate(segments_data):
            try:
                segment = ProgramSegment(
                    target_temp=seg_data.get('temp', 0),
                    time_minutes=seg_data.get('time', 0),
                    end_flag=seg_data.get('end_flag', 0)
                )
                segments.append(segment)
                
            except Exception as e:
                self.logger.error(f"创建程序段失败 (段{i+1}): {e}")
        
        return segments
    
    def write_program_to_instrument(self, channel: int, program_id: int) -> bool:
        """
        将程序写入仪表
        
        Args:
            channel: 通道号
            program_id: 程序ID
            
        Returns:
            bool: 写入成功返回True
        """
        try:
            # 从数据库加载程序
            program = self.load_program_from_database(program_id)
            if not program:
                return False
            
            # 创建程序段
            segments = self.create_program_segments(program['segments'])
            if not segments:
                self.logger.error("程序段为空")
                return False
            
            # 写入程序段到仪表
            success = self._write_segments_to_instrument(channel, segments)
            
            if success:
                # 记录程序信息
                self.running_programs[channel] = {
                    'program_id': program_id,
                    'program_name': program['name'],
                    'segments': program['segments'],
                    'current_segment': 0,
                    'start_time': None,
                    'status': 'LOADED'
                }
                
                self.program_loaded.emit(channel, program)
                self.logger.info(f"程序写入成功 - 通道{channel}: {program['name']}")
                
                # 记录系统事件
                self.database_manager.log_system_event(
                    event_type='PROGRAM',
                    message=f"程序写入成功 - 通道{channel}: {program['name']}",
                    channel=channel
                )
            
            self.program_written.emit(channel, success)
            return success
            
        except Exception as e:
            self.logger.error(f"写入程序失败 - 通道{channel}: {e}")
            self.program_written.emit(channel, False)
            return False
    
    def _write_segments_to_instrument(self, channel: int, segments: List[ProgramSegment]) -> bool:
        """
        将程序段写入仪表
        
        Args:
            channel: 通道号
            segments: 程序段列表
            
        Returns:
            bool: 写入成功返回True
        """
        try:
            if not self.serial_manager.is_connected:
                self.logger.error("串口未连接")
                return False
            
            # 这里应该实现具体的程序段写入逻辑
            # 根据宇电协议，需要逐个写入程序段
            for i, segment in enumerate(segments):
                # 计算程序段地址 (0x50 + 段号)
                segment_addr = 0x50 + i
                
                # 将程序段转换为字节数据
                segment_bytes = segment.to_bytes()
                
                # 这里需要根据实际协议实现写入逻辑
                # 暂时返回True表示写入成功
                self.logger.debug(f"写入程序段{i+1} - 通道{channel}: {segment}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"写入程序段失败: {e}")
            return False
    
    def start_program(self, channel: int) -> bool:
        """
        启动程序
        
        Args:
            channel: 通道号
            
        Returns:
            bool: 启动成功返回True
        """
        try:
            if channel not in self.running_programs:
                self.logger.error(f"通道{channel}没有加载程序")
                return False
            
            # 发送启动命令
            success = self.serial_manager.send_control_command(channel, 'START')
            
            if success:
                from datetime import datetime
                self.running_programs[channel]['start_time'] = datetime.now()
                self.running_programs[channel]['status'] = 'RUNNING'
                
                self.program_started.emit(channel)
                self.logger.info(f"程序启动成功 - 通道{channel}")
                
                # 记录系统事件
                self.database_manager.log_system_event(
                    event_type='PROGRAM',
                    message=f"程序启动 - 通道{channel}",
                    channel=channel
                )
            
            return success
            
        except Exception as e:
            self.logger.error(f"启动程序失败 - 通道{channel}: {e}")
            return False
    
    def stop_program(self, channel: int) -> bool:
        """
        停止程序
        
        Args:
            channel: 通道号
            
        Returns:
            bool: 停止成功返回True
        """
        try:
            # 发送停止命令
            success = self.serial_manager.send_control_command(channel, 'STOP')
            
            if success:
                if channel in self.running_programs:
                    self.running_programs[channel]['status'] = 'STOPPED'
                
                self.program_stopped.emit(channel)
                self.logger.info(f"程序停止成功 - 通道{channel}")
                
                # 记录系统事件
                self.database_manager.log_system_event(
                    event_type='PROGRAM',
                    message=f"程序停止 - 通道{channel}",
                    channel=channel
                )
            
            return success
            
        except Exception as e:
            self.logger.error(f"停止程序失败 - 通道{channel}: {e}")
            return False
    
    def pause_program(self, channel: int) -> bool:
        """
        暂停程序
        
        Args:
            channel: 通道号
            
        Returns:
            bool: 暂停成功返回True
        """
        try:
            # 发送暂停命令
            success = self.serial_manager.send_control_command(channel, 'HOLD')
            
            if success:
                if channel in self.running_programs:
                    self.running_programs[channel]['status'] = 'PAUSED'
                
                self.logger.info(f"程序暂停成功 - 通道{channel}")
                
                # 记录系统事件
                self.database_manager.log_system_event(
                    event_type='PROGRAM',
                    message=f"程序暂停 - 通道{channel}",
                    channel=channel
                )
            
            return success
            
        except Exception as e:
            self.logger.error(f"暂停程序失败 - 通道{channel}: {e}")
            return False
    
    def get_program_status(self, channel: int) -> Optional[Dict[str, Any]]:
        """
        获取程序状态
        
        Args:
            channel: 通道号
            
        Returns:
            Dict[str, Any]: 程序状态信息
        """
        return self.running_programs.get(channel)
    
    def get_all_program_status(self) -> Dict[int, Dict[str, Any]]:
        """
        获取所有通道的程序状态
        
        Returns:
            Dict[int, Dict[str, Any]]: 所有通道的程序状态
        """
        return self.running_programs.copy()
    
    def clear_program(self, channel: int):
        """
        清除通道程序
        
        Args:
            channel: 通道号
        """
        if channel in self.running_programs:
            del self.running_programs[channel]
            self.logger.info(f"清除通道{channel}程序")
    
    def validate_program_segments(self, segments: List[Dict[str, Any]]) -> List[str]:
        """
        验证程序段
        
        Args:
            segments: 程序段数据列表
            
        Returns:
            List[str]: 验证错误列表
        """
        errors = []
        
        if not segments:
            errors.append("程序段不能为空")
            return errors
        
        for i, segment in enumerate(segments):
            segment_num = i + 1
            
            # 检查温度
            temp = segment.get('temp', 0)
            if temp < 0 or temp > 1500:
                errors.append(f"段{segment_num}: 温度超出范围 (0-1500°C)")
            
            # 检查时间
            time_min = segment.get('time', 0)
            if time_min < 0 or time_min > 9999:
                errors.append(f"段{segment_num}: 时间超出范围 (0-9999分钟)")
            
            # 检查结束标志
            end_flag = segment.get('end_flag', 0)
            if end_flag not in [0, -121]:
                errors.append(f"段{segment_num}: 结束标志无效 (应为0或-121)")
        
        # 检查最后一段是否有结束标志
        if segments:
            last_segment = segments[-1]
            if last_segment.get('end_flag', 0) != -121:
                errors.append("最后一段必须设置结束标志(-121)")
        
        return errors
