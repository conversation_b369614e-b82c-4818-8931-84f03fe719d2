#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时曲线图控件
显示8通道的实时温度曲线
"""

import time
from collections import deque
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.dates as mdates
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QCheckBox, 
                            QLabel, QSpinBox, QPushButton, QGroupBox,
                            QGridLayout, QComboBox)
from PyQt5.QtCore import QTimer, Qt


class ChartWidget(QWidget):
    """实时曲线图控件类"""
    
    def __init__(self):
        """初始化曲线图控件"""
        super().__init__()
        
        # 数据存储 - 每个通道最多保存1000个数据点
        self.max_points = 1000
        self.channel_data = {}
        for i in range(1, 9):
            self.channel_data[i] = {
                'time': deque(maxlen=self.max_points),
                'pv': deque(maxlen=self.max_points),
                'sv': deque(maxlen=self.max_points),
                'mv': deque(maxlen=self.max_points)
            }
        
        # 通道颜色
        self.channel_colors = [
            '#FF0000', '#00FF00', '#0000FF', '#FFFF00',
            '#FF00FF', '#00FFFF', '#800000', '#008000'
        ]
        
        # 通道可见性
        self.channel_visible = {i: True for i in range(1, 9)}
        self.pv_visible = True
        self.sv_visible = True
        self.mv_visible = False
        
        self._init_ui()
        self._init_chart()
        
        # 更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._update_chart)
        self.update_timer.start(1000)  # 每秒更新一次
    
    def _init_ui(self):
        """初始化用户界面"""
        main_layout = QVBoxLayout(self)
        
        # 控制面板
        self._create_control_panel(main_layout)
        
        # 图表区域
        self._create_chart_area(main_layout)
    
    def _create_control_panel(self, parent_layout):
        """创建控制面板"""
        control_group = QGroupBox("显示控制")
        parent_layout.addWidget(control_group)
        
        control_layout = QGridLayout(control_group)
        
        # 数据类型选择
        data_type_label = QLabel("数据类型:")
        control_layout.addWidget(data_type_label, 0, 0)
        
        self.pv_checkbox = QCheckBox("PV (当前值)")
        self.pv_checkbox.setChecked(True)
        self.pv_checkbox.stateChanged.connect(self._on_pv_visibility_changed)
        control_layout.addWidget(self.pv_checkbox, 0, 1)
        
        self.sv_checkbox = QCheckBox("SV (设定值)")
        self.sv_checkbox.setChecked(True)
        self.sv_checkbox.stateChanged.connect(self._on_sv_visibility_changed)
        control_layout.addWidget(self.sv_checkbox, 0, 2)
        
        self.mv_checkbox = QCheckBox("MV (输出值)")
        self.mv_checkbox.setChecked(False)
        self.mv_checkbox.stateChanged.connect(self._on_mv_visibility_changed)
        control_layout.addWidget(self.mv_checkbox, 0, 3)
        
        # 通道选择
        channels_label = QLabel("通道:")
        control_layout.addWidget(channels_label, 1, 0)
        
        self.channel_checkboxes = []
        for i in range(8):
            checkbox = QCheckBox(f"CH{i+1}")
            checkbox.setChecked(True)
            checkbox.setStyleSheet(f"QCheckBox {{ color: {self.channel_colors[i]}; font-weight: bold; }}")
            checkbox.stateChanged.connect(lambda state, ch=i+1: self._on_channel_visibility_changed(ch, state))
            self.channel_checkboxes.append(checkbox)
            
            row = 1 + (i // 4)
            col = 1 + (i % 4)
            control_layout.addWidget(checkbox, row, col)
        
        # 时间范围选择
        time_range_label = QLabel("时间范围:")
        control_layout.addWidget(time_range_label, 3, 0)
        
        self.time_range_combo = QComboBox()
        self.time_range_combo.addItems(["最近10分钟", "最近30分钟", "最近1小时", "最近2小时", "全部"])
        self.time_range_combo.setCurrentText("最近30分钟")
        self.time_range_combo.currentTextChanged.connect(self._on_time_range_changed)
        control_layout.addWidget(self.time_range_combo, 3, 1)
        
        # 清除数据按钮
        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(self._clear_data)
        control_layout.addWidget(clear_btn, 3, 2)
        
        # 导出图表按钮
        export_btn = QPushButton("导出图表")
        export_btn.clicked.connect(self._export_chart)
        control_layout.addWidget(export_btn, 3, 3)
    
    def _create_chart_area(self, parent_layout):
        """创建图表区域"""
        # 创建matplotlib图形
        self.figure = Figure(figsize=(12, 6), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        parent_layout.addWidget(self.canvas)
        
        # 设置图形样式
        self.figure.patch.set_facecolor('white')
    
    def _init_chart(self):
        """初始化图表"""
        self.figure.clear()
        
        # 创建子图
        self.ax = self.figure.add_subplot(111)
        
        # 设置标题和标签
        self.ax.set_title('8通道实时温度曲线', fontsize=14, fontweight='bold')
        self.ax.set_xlabel('时间')
        self.ax.set_ylabel('温度 (°C)')
        
        # 设置网格
        self.ax.grid(True, alpha=0.3)
        
        # 设置时间格式
        self.ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
        self.ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=5))
        
        # 自动调整布局
        self.figure.tight_layout()
        
        # 初始化线条字典
        self.lines = {}
        
        self.canvas.draw()
    
    def add_data_point(self, channel, data):
        """
        添加数据点
        
        Args:
            channel: 通道号 (1-8)
            data: 数据字典，包含pv, sv, mv, timestamp等
        """
        if channel not in self.channel_data:
            return
        
        # 获取时间戳
        timestamp = data.get('timestamp', time.time())
        dt = datetime.fromtimestamp(timestamp)
        
        # 添加数据点
        self.channel_data[channel]['time'].append(dt)
        self.channel_data[channel]['pv'].append(data.get('pv', 0))
        self.channel_data[channel]['sv'].append(data.get('sv', 0))
        self.channel_data[channel]['mv'].append(data.get('mv', 0))
    
    def _update_chart(self):
        """更新图表"""
        self.ax.clear()
        
        # 重新设置图表属性
        self.ax.set_title('8通道实时温度曲线', fontsize=14, fontweight='bold')
        self.ax.set_xlabel('时间')
        self.ax.set_ylabel('温度 (°C)')
        self.ax.grid(True, alpha=0.3)
        
        # 获取时间范围
        time_range = self._get_time_range()
        
        # 绘制每个通道的数据
        for channel in range(1, 9):
            if not self.channel_visible[channel]:
                continue
            
            channel_data = self.channel_data[channel]
            if not channel_data['time']:
                continue
            
            # 过滤时间范围内的数据
            times, pvs, svs, mvs = self._filter_data_by_time(channel_data, time_range)
            
            if not times:
                continue
            
            color = self.channel_colors[channel - 1]
            
            # 绘制PV线
            if self.pv_visible:
                self.ax.plot(times, pvs, color=color, linewidth=2, 
                           label=f'CH{channel} PV', alpha=0.8)
            
            # 绘制SV线
            if self.sv_visible:
                self.ax.plot(times, svs, color=color, linewidth=1, 
                           linestyle='--', label=f'CH{channel} SV', alpha=0.6)
            
            # 绘制MV线（在右侧Y轴）
            if self.mv_visible:
                ax2 = self.ax.twinx()
                ax2.plot(times, mvs, color=color, linewidth=1, 
                        linestyle=':', label=f'CH{channel} MV', alpha=0.4)
                ax2.set_ylabel('输出 (%)')
                ax2.set_ylim(0, 100)
        
        # 设置时间轴格式
        if time_range:
            self.ax.set_xlim(time_range)
        
        self.ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        self.ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=5))
        
        # 显示图例
        if self.ax.get_lines():
            self.ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        # 自动调整Y轴范围
        self.ax.relim()
        self.ax.autoscale_view()
        
        # 旋转时间标签
        self.figure.autofmt_xdate()
        
        # 调整布局
        self.figure.tight_layout()
        
        # 刷新画布
        self.canvas.draw()
    
    def _get_time_range(self):
        """获取时间范围"""
        current_time = datetime.now()
        range_text = self.time_range_combo.currentText()
        
        if range_text == "最近10分钟":
            start_time = current_time - timedelta(minutes=10)
        elif range_text == "最近30分钟":
            start_time = current_time - timedelta(minutes=30)
        elif range_text == "最近1小时":
            start_time = current_time - timedelta(hours=1)
        elif range_text == "最近2小时":
            start_time = current_time - timedelta(hours=2)
        else:  # 全部
            return None
        
        return (start_time, current_time)
    
    def _filter_data_by_time(self, channel_data, time_range):
        """根据时间范围过滤数据"""
        if not time_range:
            return (list(channel_data['time']), 
                   list(channel_data['pv']), 
                   list(channel_data['sv']), 
                   list(channel_data['mv']))
        
        start_time, end_time = time_range
        
        filtered_times = []
        filtered_pvs = []
        filtered_svs = []
        filtered_mvs = []
        
        for i, dt in enumerate(channel_data['time']):
            if start_time <= dt <= end_time:
                filtered_times.append(dt)
                filtered_pvs.append(channel_data['pv'][i])
                filtered_svs.append(channel_data['sv'][i])
                filtered_mvs.append(channel_data['mv'][i])
        
        return filtered_times, filtered_pvs, filtered_svs, filtered_mvs
    
    def _on_pv_visibility_changed(self, state):
        """PV可见性改变"""
        self.pv_visible = state == Qt.Checked
    
    def _on_sv_visibility_changed(self, state):
        """SV可见性改变"""
        self.sv_visible = state == Qt.Checked
    
    def _on_mv_visibility_changed(self, state):
        """MV可见性改变"""
        self.mv_visible = state == Qt.Checked
    
    def _on_channel_visibility_changed(self, channel, state):
        """通道可见性改变"""
        self.channel_visible[channel] = state == Qt.Checked
    
    def _on_time_range_changed(self, range_text):
        """时间范围改变"""
        pass  # 在_update_chart中处理
    
    def _clear_data(self):
        """清除所有数据"""
        for channel in range(1, 9):
            self.channel_data[channel]['time'].clear()
            self.channel_data[channel]['pv'].clear()
            self.channel_data[channel]['sv'].clear()
            self.channel_data[channel]['mv'].clear()
        
        self._update_chart()
    
    def _export_chart(self):
        """导出图表"""
        from PyQt5.QtWidgets import QFileDialog
        
        filename, _ = QFileDialog.getSaveFileName(
            self, "导出图表", f"chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png",
            "PNG files (*.png);;PDF files (*.pdf);;SVG files (*.svg)"
        )
        
        if filename:
            self.figure.savefig(filename, dpi=300, bbox_inches='tight')
    
    def get_data_summary(self):
        """获取数据摘要"""
        summary = {}
        for channel in range(1, 9):
            data = self.channel_data[channel]
            if data['pv']:
                summary[channel] = {
                    'count': len(data['pv']),
                    'latest_pv': data['pv'][-1],
                    'latest_sv': data['sv'][-1],
                    'latest_mv': data['mv'][-1],
                    'latest_time': data['time'][-1]
                }
        return summary
