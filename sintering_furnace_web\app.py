#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
8通道烧结炉控制软件 - Web版本主程序
基于FastAPI的Web API服务
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, List, Optional
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# 导入现有的核心模块
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'sintering_furnace_control'))

from src.core.config_manager import ConfigManager
from src.communication.serial_manager import SerialManager
from src.database.database_manager import DatabaseManager
from core.web_monitoring_manager import WebMonitoringManager
from core.web_program_manager import WebProgramManager

# 数据模型
class ChannelData(BaseModel):
    channel: int
    pv: float
    sv: float
    mv: float
    status: str
    timestamp: str

class ChannelConfig(BaseModel):
    channel: int
    address: int
    enabled: bool

class ProgramSegment(BaseModel):
    segment: int
    target_temp: float
    ramp_rate: float
    hold_time: int

class ControlCommand(BaseModel):
    channel: int
    command: str  # START, STOP, HOLD

# 全局变量
app = FastAPI(title="烧结炉控制系统", description="8通道烧结炉Web控制界面", version="2.0")
config_manager = None
serial_manager = None
database_manager = None
monitoring_manager = None
program_manager = None

# WebSocket连接管理
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                # 连接已断开，移除
                self.active_connections.remove(connection)

manager = ConnectionManager()

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    global config_manager, serial_manager, database_manager, monitoring_manager, program_manager
    
    # 初始化配置管理器
    config_manager = ConfigManager()
    
    # 初始化数据库管理器
    database_manager = DatabaseManager(config_manager.get("database.path"))
    
    # 初始化串口管理器
    serial_manager = SerialManager(config_manager)
    
    # 初始化Web监控管理器
    monitoring_manager = WebMonitoringManager(serial_manager, database_manager)

    # 初始化Web程序管理器
    program_manager = WebProgramManager(serial_manager, database_manager)
    
    # 启动Web监控
    await monitoring_manager.start_monitoring()

    logging.info("Web应用启动完成")

# API路由
@app.get("/")
async def read_root():
    """返回主页面"""
    return FileResponse('static/index.html')

@app.get("/api/channels")
async def get_channels():
    """获取所有通道状态"""
    try:
        channels = []
        for channel in range(1, 9):
            if monitoring_manager:
                data = monitoring_manager.get_channel_data(channel)
                if data:
                    channels.append(ChannelData(
                        channel=channel,
                        pv=data.get('pv', 0),
                        sv=data.get('sv', 0),
                        mv=data.get('mv', 0),
                        status=data.get('status', 'UNKNOWN'),
                        timestamp=datetime.now().isoformat()
                    ))
        return {"channels": channels}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/channels/{channel_id}")
async def get_channel(channel_id: int):
    """获取指定通道状态"""
    try:
        if not (1 <= channel_id <= 8):
            raise HTTPException(status_code=400, detail="通道号必须在1-8之间")
        
        if monitoring_manager:
            data = monitoring_manager.get_channel_data(channel_id)
            if data:
                return ChannelData(
                    channel=channel_id,
                    pv=data.get('pv', 0),
                    sv=data.get('sv', 0),
                    mv=data.get('mv', 0),
                    status=data.get('status', 'UNKNOWN'),
                    timestamp=datetime.now().isoformat()
                )
        
        raise HTTPException(status_code=404, detail="通道数据未找到")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/channels/{channel_id}/control")
async def control_channel(channel_id: int, command: ControlCommand):
    """控制指定通道"""
    try:
        if not (1 <= channel_id <= 8):
            raise HTTPException(status_code=400, detail="通道号必须在1-8之间")
        
        if command.command not in ["START", "STOP", "HOLD"]:
            raise HTTPException(status_code=400, detail="无效的控制命令")
        
        # 执行控制命令
        if serial_manager:
            success = serial_manager.send_control_command(channel_id, command.command)
            if success:
                return {"message": f"通道{channel_id}执行{command.command}命令成功"}
            else:
                raise HTTPException(status_code=500, detail="命令执行失败")
        
        raise HTTPException(status_code=500, detail="串口管理器未初始化")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/config")
async def get_config():
    """获取系统配置"""
    try:
        if config_manager:
            return {
                "serial_port": config_manager.get("serial.port"),
                "baud_rate": config_manager.get("serial.baud_rate"),
                "channels": [
                    {
                        "channel": i,
                        "address": config_manager.get(f"channel.{i}.address"),
                        "enabled": config_manager.get(f"channel.{i}.enabled")
                    }
                    for i in range(1, 9)
                ]
            }
        raise HTTPException(status_code=500, detail="配置管理器未初始化")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/config")
async def update_config(config_data: dict):
    """更新系统配置"""
    try:
        if config_manager:
            # 更新配置
            for key, value in config_data.items():
                config_manager.set(key, value)
            config_manager.save()
            return {"message": "配置更新成功"}
        
        raise HTTPException(status_code=500, detail="配置管理器未初始化")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/programs")
async def get_programs():
    """获取所有程序列表"""
    try:
        if program_manager:
            programs = await program_manager.get_all_programs()
            return {"programs": programs}
        raise HTTPException(status_code=500, detail="程序管理器未初始化")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/programs/{program_id}")
async def get_program(program_id: int):
    """获取指定程序"""
    try:
        if program_manager:
            program = await program_manager.load_program_from_database(program_id)
            if program:
                return program_manager.program_to_dict(program)
            raise HTTPException(status_code=404, detail="程序未找到")
        raise HTTPException(status_code=500, detail="程序管理器未初始化")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/programs")
async def create_program(program_data: dict):
    """创建新程序"""
    try:
        if program_manager:
            program = program_manager.dict_to_program(program_data)
            program.program_id = 0  # 新程序ID为0
            success = await program_manager.save_program_to_database(program)
            if success:
                return {"message": "程序创建成功"}
            raise HTTPException(status_code=500, detail="程序创建失败")
        raise HTTPException(status_code=500, detail="程序管理器未初始化")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/api/programs/{program_id}")
async def update_program(program_id: int, program_data: dict):
    """更新程序"""
    try:
        if program_manager:
            program_data['id'] = program_id
            program = program_manager.dict_to_program(program_data)
            success = await program_manager.save_program_to_database(program)
            if success:
                return {"message": "程序更新成功"}
            raise HTTPException(status_code=500, detail="程序更新失败")
        raise HTTPException(status_code=500, detail="程序管理器未初始化")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/programs/{program_id}")
async def delete_program(program_id: int):
    """删除程序"""
    try:
        if program_manager:
            success = await program_manager.delete_program(program_id)
            if success:
                return {"message": "程序删除成功"}
            raise HTTPException(status_code=500, detail="程序删除失败")
        raise HTTPException(status_code=500, detail="程序管理器未初始化")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/history")
async def get_history_data(channel: str, start_time: str, end_time: str):
    """获取历史数据"""
    try:
        if monitoring_manager:
            start_dt = datetime.fromisoformat(start_time.replace('T', ' '))
            end_dt = datetime.fromisoformat(end_time.replace('T', ' '))

            if channel == 'all':
                # 获取所有通道数据
                all_data = []
                for ch in range(1, 9):
                    data = await monitoring_manager.get_historical_data(ch, start_dt, end_dt)
                    all_data.extend(data)
                return {"data": all_data}
            else:
                # 获取指定通道数据
                channel_num = int(channel)
                data = await monitoring_manager.get_historical_data(channel_num, start_dt, end_dt)
                return {"data": data}

        raise HTTPException(status_code=500, detail="监控管理器未初始化")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/export")
async def export_data(channel: str, start_time: str, end_time: str, format: str = 'csv'):
    """导出数据"""
    try:
        if monitoring_manager:
            start_dt = datetime.fromisoformat(start_time.replace('T', ' '))
            end_dt = datetime.fromisoformat(end_time.replace('T', ' '))

            channels = [int(channel)] if channel != 'all' else list(range(1, 9))
            file_path = await monitoring_manager.export_data(channels, start_dt, end_dt, format)

            return FileResponse(
                file_path,
                media_type='application/octet-stream',
                filename=f'furnace_data_{datetime.now().strftime("%Y%m%d_%H%M%S")}.{format}'
            )

        raise HTTPException(status_code=500, detail="监控管理器未初始化")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket连接端点"""
    await manager.connect(websocket)

    # 将连接添加到监控管理器
    if monitoring_manager:
        monitoring_manager.add_websocket_connection(websocket)

    try:
        while True:
            data = await websocket.receive_text()
            # 处理客户端消息
            message = json.loads(data)
            if message.get("type") == "ping":
                await manager.send_personal_message(
                    json.dumps({"type": "pong"}),
                    websocket
                )
    except WebSocketDisconnect:
        manager.disconnect(websocket)
        if monitoring_manager:
            monitoring_manager.remove_websocket_connection(websocket)

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 启动服务器
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
